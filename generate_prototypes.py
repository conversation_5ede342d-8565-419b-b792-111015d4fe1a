#!/usr/bin/env python3
"""
VPP-AI 原型界面批量生成脚本
=============================

快速生成所有原型界面文件
"""

import os
from pathlib import Path

# 定义所有模块和对应的页面
MODULES = {
    'dashboard': {
        'name': '主控制台',
        'icon': '📊',
        'color': '#667eea',
        'pages': [
            ('main.html', '主控制台', '系统总览和实时监控'),
            ('overview.html', '系统总览', '整体运行状态概览'),
            ('realtime.html', '实时监控', '实时数据监控'),
            ('alerts.html', '告警中心', '系统告警管理'),
            ('kpi.html', 'KPI仪表板', '关键性能指标'),
            ('trends.html', '趋势分析', '数据趋势分析'),
            ('comparison.html', '对比分析', '历史数据对比'),
            ('3d_view.html', '3D可视化', '三维场景展示'),
            ('mobile.html', '移动端', '移动设备界面'),
            ('emergency.html', '应急控制', '紧急情况处理'),
            ('manual_control.html', '手动控制', '手动操作界面'),
            ('export_data.html', '数据导出', '数据导出功能')
        ]
    },
    'solar': {
        'name': '光伏发电系统',
        'icon': '🌞',
        'color': '#f39c12',
        'pages': [
            ('index.html', '光伏系统首页', '光伏系统导航'),
            ('monitoring.html', '实时监控', '光伏发电实时监控'),
            ('panels.html', '光伏板管理', '光伏板设备管理'),
            ('prediction.html', '发电预测', 'AI发电功率预测'),
            ('efficiency.html', '效率分析', '发电效率分析'),
            ('inverter.html', '逆变器管理', '逆变器监控管理'),
            ('maintenance.html', '维护管理', '设备维护管理'),
            ('weather_impact.html', '天气影响分析', '天气对发电的影响'),
            ('performance.html', '性能评估', '系统性能评估'),
            ('alerts.html', '告警管理', '光伏系统告警'),
            ('reports.html', '报表中心', '光伏系统报表'),
            ('optimization.html', '发电优化', '发电优化策略'),
            ('comparison.html', '对比分析', '发电数据对比'),
            ('export.html', '数据导出', '数据导出功能'),
            ('settings.html', '系统设置', '光伏系统设置')
        ]
    },
    'storage': {
        'name': '储能管理系统',
        'icon': '🔋',
        'color': '#74b9ff',
        'pages': [
            ('index.html', '储能系统首页', '储能系统导航'),
            ('monitoring.html', '实时监控', '电池状态实时监控'),
            ('battery_management.html', '电池管理', '电池设备管理'),
            ('charge_control.html', '充放电控制', '充放电策略控制'),
            ('soc_management.html', 'SOC管理', '荷电状态管理'),
            ('thermal_management.html', '热管理系统', '电池温度管理'),
            ('safety_protection.html', '安全保护', '电池安全保护'),
            ('life_analysis.html', '寿命分析', '电池寿命分析'),
            ('optimization.html', '优化策略', '储能优化策略'),
            ('maintenance.html', '维护管理', '电池维护管理'),
            ('performance.html', '性能评估', '储能性能评估'),
            ('alerts.html', '告警管理', '储能系统告警'),
            ('energy_flow.html', '能量流分析', '能量流向分析'),
            ('grid_services.html', '电网服务', '电网辅助服务'),
            ('economics.html', '经济分析', '储能经济分析'),
            ('forecasting.html', '状态预测', '电池状态预测'),
            ('reports.html', '报表中心', '储能系统报表'),
            ('settings.html', '系统设置', '储能系统设置')
        ]
    },
    'load': {
        'name': '负荷管理系统',
        'icon': '⚡',
        'color': '#00b894',
        'pages': [
            ('index.html', '负荷系统首页', '负荷系统导航'),
            ('monitoring.html', '实时监控', '负荷实时监控'),
            ('devices.html', '设备管理', '负荷设备管理'),
            ('prediction.html', '负荷预测', 'AI负荷预测'),
            ('scheduling.html', '负荷调度', '负荷调度管理'),
            ('demand_response.html', '需求响应', '需求响应管理'),
            ('load_balancing.html', '负荷平衡', '负荷平衡控制'),
            ('peak_shaving.html', '削峰填谷', '削峰填谷策略'),
            ('flexibility.html', '灵活性管理', '负荷灵活性'),
            ('priority.html', '优先级管理', '负荷优先级'),
            ('automation.html', '自动化控制', '负荷自动化'),
            ('analysis.html', '用电分析', '用电行为分析'),
            ('alerts.html', '告警管理', '负荷系统告警'),
            ('reports.html', '报表中心', '负荷系统报表'),
            ('optimization.html', '优化策略', '负荷优化策略'),
            ('settings.html', '系统设置', '负荷系统设置')
        ]
    },
    'grid': {
        'name': '电网交易系统',
        'icon': '🔌',
        'color': '#6c5ce7',
        'pages': [
            ('index.html', '电网系统首页', '电网系统导航'),
            ('monitoring.html', '实时监控', '电网状态监控'),
            ('trading.html', '电力交易', '电力交易管理'),
            ('pricing.html', '电价管理', '电价监控分析'),
            ('market.html', '电力市场', '电力市场参与'),
            ('revenue.html', '收益分析', '售电收益分析'),
            ('contracts.html', '合同管理', '电力合同管理'),
            ('settlement.html', '结算管理', '电费结算管理'),
            ('demand_response.html', '需求响应', '需求响应参与'),
            ('ancillary_services.html', '辅助服务', '电网辅助服务'),
            ('forecasting.html', '电价预测', '电价预测分析'),
            ('risk_management.html', '风险管理', '交易风险管理'),
            ('alerts.html', '告警管理', '电网系统告警'),
            ('reports.html', '报表中心', '电网系统报表')
        ]
    },
    'weather': {
        'name': '天气数据系统',
        'icon': '🌤️',
        'color': '#00cec9',
        'pages': [
            ('index.html', '天气系统首页', '天气系统导航'),
            ('monitoring.html', '实时监控', '天气实时监控'),
            ('forecasting.html', '天气预报', '天气预报分析'),
            ('solar_radiation.html', '太阳辐射', '太阳辐射监测'),
            ('temperature.html', '温度监控', '温度数据监控'),
            ('wind.html', '风力监控', '风力数据监控'),
            ('humidity.html', '湿度监控', '湿度数据监控'),
            ('alerts.html', '天气预警', '天气预警管理'),
            ('historical.html', '历史数据', '历史天气数据'),
            ('reports.html', '报表中心', '天气数据报表')
        ]
    },
    'optimization': {
        'name': '智能优化引擎',
        'icon': '🧠',
        'color': '#fd79a8',
        'pages': [
            ('index.html', '优化系统首页', '优化系统导航'),
            ('ai_prediction.html', 'AI预测', 'AI预测模型'),
            ('strategy.html', '优化策略', '优化策略管理'),
            ('algorithms.html', '算法管理', '优化算法管理'),
            ('scenarios.html', '场景分析', '优化场景分析'),
            ('results.html', '优化结果', '优化结果分析'),
            ('performance.html', '性能评估', '优化性能评估'),
            ('model_training.html', '模型训练', 'AI模型训练'),
            ('decision_support.html', '决策支持', '智能决策支持'),
            ('simulation.html', '仿真分析', '系统仿真分析'),
            ('alerts.html', '告警管理', '优化系统告警'),
            ('reports.html', '报表中心', '优化系统报表')
        ]
    },
    'digital_twin': {
        'name': '数字孪生系统',
        'icon': '🏭',
        'color': '#e17055',
        'pages': [
            ('index.html', '数字孪生首页', '数字孪生导航'),
            ('3d_modeling.html', '3D建模', '三维建模管理'),
            ('simulation.html', '实时仿真', '实时仿真分析'),
            ('scenarios.html', '场景分析', '仿真场景分析'),
            ('virtual_commissioning.html', '虚拟调试', '虚拟调试功能'),
            ('predictive_maintenance.html', '预测性维护', '预测性维护'),
            ('optimization.html', '仿真优化', '仿真优化分析'),
            ('reports.html', '报表中心', '数字孪生报表')
        ]
    },
    'system': {
        'name': '系统管理',
        'icon': '⚙️',
        'color': '#636e72',
        'pages': [
            ('index.html', '系统管理首页', '系统管理导航'),
            ('users.html', '用户管理', '用户账户管理'),
            ('roles.html', '角色权限', '角色权限管理'),
            ('security.html', '安全管理', '系统安全管理'),
            ('database.html', '数据库管理', '数据库管理'),
            ('backup.html', '备份恢复', '数据备份恢复'),
            ('monitoring.html', '系统监控', '系统性能监控'),
            ('logging.html', '日志管理', '系统日志管理'),
            ('configuration.html', '系统配置', '系统参数配置'),
            ('maintenance.html', '系统维护', '系统维护管理')
        ]
    },
    'reports': {
        'name': '报表分析',
        'icon': '📈',
        'color': '#a29bfe',
        'pages': [
            ('index.html', '报表中心首页', '报表中心导航'),
            ('dashboard.html', '报表仪表板', '报表总览'),
            ('daily.html', '日报表', '日报表生成'),
            ('weekly.html', '周报表', '周报表生成'),
            ('monthly.html', '月报表', '月报表生成'),
            ('annual.html', '年报表', '年报表生成'),
            ('financial.html', '财务报表', '财务分析报表'),
            ('operational.html', '运营报表', '运营分析报表'),
            ('performance.html', '性能报表', '性能分析报表'),
            ('custom.html', '自定义报表', '自定义报表'),
            ('export.html', '报表导出', '报表导出功能'),
            ('templates.html', '报表模板', '报表模板管理'),
            ('scheduling.html', '定时报表', '定时报表生成')
        ]
    }
}

def create_html_template(module, page_name, page_title, page_desc, module_info):
    """创建HTML模板"""
    template = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{page_title} - VPP-AI</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, {module_info['color']}22 0%, {module_info['color']}44 100%);
            min-height: 100vh;
            color: #333;
        }}
        
        .header {{
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }}
        
        .title {{
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }}
        
        .subtitle {{
            text-align: center;
            color: #7f8c8d;
            font-size: 16px;
            margin-bottom: 20px;
        }}
        
        .nav-breadcrumb {{
            text-align: center;
            margin-bottom: 20px;
        }}
        
        .nav-breadcrumb a {{
            color: #3498db;
            text-decoration: none;
            margin: 0 5px;
        }}
        
        .content {{
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            min-height: 500px;
        }}
        
        .back-btn {{
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 20px;
            transition: all 0.3s ease;
        }}
        
        .back-btn:hover {{
            background: #2980b9;
            transform: translateY(-2px);
        }}
        
        .feature-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }}
        
        .feature-card {{
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid {module_info['color']};
        }}
        
        .feature-title {{
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }}
        
        .feature-desc {{
            color: #7f8c8d;
            line-height: 1.6;
        }}
        
        .status-indicator {{
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #27ae60;
            margin-right: 8px;
        }}
        
        .placeholder {{
            text-align: center;
            color: #7f8c8d;
            font-size: 18px;
            margin: 50px 0;
        }}
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="title">
                <span>{module_info['icon']}</span>
                <span>{page_title}</span>
            </h1>
            <p class="subtitle">{page_desc}</p>
            <div class="nav-breadcrumb">
                <a href="../index.html">🏠 首页</a> > 
                <a href="../dashboard/main.html">📊 控制台</a> > 
                <a href="index.html">{module_info['icon']} {module_info['name']}</a> > 
                <span>{page_title}</span>
            </div>
        </div>
    </div>

    <a href="index.html" class="back-btn">← 返回{module_info['name']}</a>

    <div class="content">
        <h2 style="color: #2c3e50; margin-bottom: 20px;">
            <span class="status-indicator"></span>{page_title}
        </h2>
        
        <div class="placeholder">
            🚧 此页面正在开发中，敬请期待...
            <br><br>
            <small>这里将展示{page_desc}的详细功能界面</small>
        </div>
        
        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-title">📊 数据展示</div>
                <div class="feature-desc">实时数据图表和统计信息展示</div>
            </div>
            <div class="feature-card">
                <div class="feature-title">⚙️ 功能控制</div>
                <div class="feature-desc">相关功能的操作控制界面</div>
            </div>
            <div class="feature-card">
                <div class="feature-title">📈 趋势分析</div>
                <div class="feature-desc">历史数据趋势分析和预测</div>
            </div>
            <div class="feature-card">
                <div class="feature-title">🔔 告警提醒</div>
                <div class="feature-desc">相关告警信息和处理建议</div>
            </div>
        </div>
    </div>

    <div style="text-align: center; padding: 40px; color: rgba(0,0,0,0.6);">
        <p>© 2025 VPP-AI {module_info['name']} - {page_title}</p>
        <p>原型界面展示，实际功能正在开发中</p>
    </div>
</body>
</html>"""
    return template

def generate_all_pages():
    """生成所有页面"""
    base_dir = Path("原型图")
    total_pages = 0
    
    for module, module_info in MODULES.items():
        module_dir = base_dir / module
        module_dir.mkdir(exist_ok=True)
        
        for page_file, page_title, page_desc in module_info['pages']:
            # 跳过已存在的文件
            if (module_dir / page_file).exists():
                print(f"跳过已存在的文件: {module}/{page_file}")
                continue
                
            html_content = create_html_template(
                module, page_file, page_title, page_desc, module_info
            )
            
            with open(module_dir / page_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"创建页面: {module}/{page_file} - {page_title}")
            total_pages += 1
    
    print(f"\n✅ 总共生成了 {total_pages} 个页面")
    print(f"📊 预计总页面数: {sum(len(info['pages']) for info in MODULES.values())}")

if __name__ == "__main__":
    generate_all_pages()
