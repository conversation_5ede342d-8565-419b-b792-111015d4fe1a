"""
异常处理模块
===========

定义系统中使用的自定义异常类。
"""


class VPPAIException(Exception):
    """VPP-AI系统基础异常类"""
    
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class ConfigurationError(VPPAIException):
    """配置错误"""
    pass


class DatabaseError(VPPAIException):
    """数据库错误"""
    pass


class ModelError(VPPAIException):
    """模型相关错误"""
    pass


class PredictionError(ModelError):
    """预测错误"""
    pass


class OptimizationError(VPPAIException):
    """优化算法错误"""
    pass


class DeviceError(VPPAIException):
    """设备相关错误"""
    pass


class SolarPanelError(DeviceError):
    """光伏板错误"""
    pass


class BatteryError(DeviceError):
    """电池错误"""
    pass


class GridError(VPPAIException):
    """电网相关错误"""
    pass


class APIError(VPPAIException):
    """API相关错误"""
    pass


class DataValidationError(VPPAIException):
    """数据验证错误"""
    pass
