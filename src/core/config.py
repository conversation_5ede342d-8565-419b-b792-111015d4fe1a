"""
配置管理模块
===========

负责加载和管理系统配置。
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field


class SystemConfig(BaseModel):
    """系统配置"""
    name: str = "VPP-AI"
    version: str = "1.0.0"
    description: str = "新能源微网决策系统"
    timezone: str = "Asia/Shanghai"
    debug: bool = True


class DatabaseConfig(BaseModel):
    """数据库配置"""
    postgres: Dict[str, Any] = Field(default_factory=dict)
    redis: Dict[str, Any] = Field(default_factory=dict)
    mongodb: Dict[str, Any] = Field(default_factory=dict)


class SolarConfig(BaseModel):
    """光伏系统配置"""
    panel_area: float = 1000.0
    panel_efficiency: float = 0.22
    panel_orientation: int = 180
    panel_tilt: int = 30
    inverter_efficiency: float = 0.95
    inverter_capacity: float = 200.0


class StorageConfig(BaseModel):
    """储能系统配置"""
    capacity: float = 500.0
    max_charge_power: float = 100.0
    max_discharge_power: float = 100.0
    charge_efficiency: float = 0.95
    discharge_efficiency: float = 0.95
    min_soc: float = 0.1
    max_soc: float = 0.9
    cycle_life: int = 6000
    calendar_life: int = 15


class GridConfig(BaseModel):
    """电网配置"""
    electricity_price: Dict[str, float] = Field(default_factory=dict)
    time_periods: Dict[str, list] = Field(default_factory=dict)
    feed_in_tariff: float = 0.35


class AIModelsConfig(BaseModel):
    """AI模型配置"""
    solar_forecast: Dict[str, Any] = Field(default_factory=dict)
    load_forecast: Dict[str, Any] = Field(default_factory=dict)
    weather_api: Dict[str, Any] = Field(default_factory=dict)


class OptimizationConfig(BaseModel):
    """优化算法配置"""
    objective: str = "maximize_profit"
    horizon: int = 24
    constraints: Dict[str, bool] = Field(default_factory=dict)
    solver: str = "ECOS"


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，默认为项目根目录下的config.yaml
        """
        if config_path is None:
            # 获取项目根目录
            project_root = Path(__file__).parent.parent.parent
            config_path = project_root / "config.yaml"
        
        self.config_path = Path(config_path)
        self._config_data = {}
        self.load_config()
    
    def load_config(self) -> None:
        """加载配置文件"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self._config_data = yaml.safe_load(f) or {}
            else:
                print(f"配置文件不存在: {self.config_path}")
                self._config_data = {}
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self._config_data = {}
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self._config_data
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    @property
    def system(self) -> SystemConfig:
        """获取系统配置"""
        return SystemConfig(**self.get('system', {}))
    
    @property
    def database(self) -> DatabaseConfig:
        """获取数据库配置"""
        return DatabaseConfig(**self.get('database', {}))
    
    @property
    def solar(self) -> SolarConfig:
        """获取光伏配置"""
        return SolarConfig(**self.get('solar', {}))
    
    @property
    def storage(self) -> StorageConfig:
        """获取储能配置"""
        return StorageConfig(**self.get('storage', {}))
    
    @property
    def grid(self) -> GridConfig:
        """获取电网配置"""
        return GridConfig(**self.get('grid', {}))
    
    @property
    def ai_models(self) -> AIModelsConfig:
        """获取AI模型配置"""
        return AIModelsConfig(**self.get('ai_models', {}))
    
    @property
    def optimization(self) -> OptimizationConfig:
        """获取优化配置"""
        return OptimizationConfig(**self.get('optimization', {}))


# 全局配置实例
config = ConfigManager()
