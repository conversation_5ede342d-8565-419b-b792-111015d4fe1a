"""
日志管理模块
===========

配置和管理系统日志。
"""

import sys
from pathlib import Path
from loguru import logger
from .config import config


def setup_logger(
    log_level: str = None,
    log_file: str = None,
    rotation: str = None,
    retention: str = None
) -> None:
    """
    设置日志配置
    
    Args:
        log_level: 日志级别
        log_file: 日志文件路径
        rotation: 日志轮转配置
        retention: 日志保留配置
    """
    # 移除默认处理器
    logger.remove()
    
    # 从配置文件获取默认值
    log_level = log_level or config.get('logging.level', 'INFO')
    log_format = config.get(
        'logging.format',
        "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    )
    rotation = rotation or config.get('logging.rotation', '1 day')
    retention = retention or config.get('logging.retention', '30 days')
    
    # 控制台输出
    logger.add(
        sys.stdout,
        level=log_level,
        format=log_format,
        colorize=True
    )
    
    # 文件输出
    if log_file is None:
        # 默认日志文件路径
        project_root = Path(__file__).parent.parent.parent
        logs_dir = project_root / "logs"
        logs_dir.mkdir(exist_ok=True)
        log_file = logs_dir / "vpp_ai.log"
    
    logger.add(
        log_file,
        level=log_level,
        format=log_format,
        rotation=rotation,
        retention=retention,
        encoding="utf-8"
    )
    
    logger.info(f"日志系统已初始化，级别: {log_level}")


# 获取logger实例的便捷函数
def get_logger(name: str = None):
    """获取logger实例"""
    if name:
        return logger.bind(name=name)
    return logger


# 初始化日志系统
setup_logger()
