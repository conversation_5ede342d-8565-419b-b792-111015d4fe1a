"""
数据库管理模块
=============

管理PostgreSQL、Redis和MongoDB连接。
"""

from typing import Optional, Dict, Any
import asyncio
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
import redis
from pymongo import MongoClient
from .config import config
from .logger import get_logger
from .exceptions import DatabaseError

logger = get_logger(__name__)

# SQLAlchemy基类
Base = declarative_base()


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self._postgres_engine = None
        self._postgres_async_engine = None
        self._postgres_session = None
        self._postgres_async_session = None
        self._redis_client = None
        self._mongo_client = None
        self._mongo_db = None
    
    def get_postgres_url(self, async_mode: bool = False) -> str:
        """获取PostgreSQL连接URL"""
        db_config = config.database.postgres
        driver = "postgresql+asyncpg" if async_mode else "postgresql+psycopg2"
        
        return (
            f"{driver}://{db_config['username']}:{db_config['password']}"
            f"@{db_config['host']}:{db_config['port']}/{db_config['database']}"
        )
    
    def init_postgres(self) -> None:
        """初始化PostgreSQL连接"""
        try:
            # 同步引擎
            self._postgres_engine = create_engine(
                self.get_postgres_url(),
                echo=config.system.debug,
                pool_pre_ping=True
            )
            
            # 异步引擎
            self._postgres_async_engine = create_async_engine(
                self.get_postgres_url(async_mode=True),
                echo=config.system.debug
            )
            
            # 会话工厂
            self._postgres_session = sessionmaker(
                bind=self._postgres_engine,
                autocommit=False,
                autoflush=False
            )
            
            self._postgres_async_session = async_sessionmaker(
                bind=self._postgres_async_engine,
                class_=AsyncSession,
                autocommit=False,
                autoflush=False
            )
            
            logger.info("PostgreSQL连接已初始化")
            
        except Exception as e:
            logger.error(f"PostgreSQL初始化失败: {e}")
            raise DatabaseError(f"PostgreSQL初始化失败: {e}")
    
    def init_redis(self) -> None:
        """初始化Redis连接"""
        try:
            redis_config = config.database.redis
            self._redis_client = redis.Redis(
                host=redis_config['host'],
                port=redis_config['port'],
                db=redis_config['database'],
                password=redis_config['password'] or None,
                decode_responses=True
            )
            
            # 测试连接
            self._redis_client.ping()
            logger.info("Redis连接已初始化")
            
        except Exception as e:
            logger.error(f"Redis初始化失败: {e}")
            raise DatabaseError(f"Redis初始化失败: {e}")
    
    def init_mongodb(self) -> None:
        """初始化MongoDB连接"""
        try:
            mongo_config = config.database.mongodb
            self._mongo_client = MongoClient(
                host=mongo_config['host'],
                port=mongo_config['port']
            )
            
            self._mongo_db = self._mongo_client[mongo_config['database']]
            
            # 测试连接
            self._mongo_client.admin.command('ping')
            logger.info("MongoDB连接已初始化")
            
        except Exception as e:
            logger.error(f"MongoDB初始化失败: {e}")
            raise DatabaseError(f"MongoDB初始化失败: {e}")
    
    def init_all(self) -> None:
        """初始化所有数据库连接"""
        self.init_postgres()
        self.init_redis()
        self.init_mongodb()
    
    def get_postgres_session(self) -> Session:
        """获取PostgreSQL同步会话"""
        if self._postgres_session is None:
            self.init_postgres()
        return self._postgres_session()
    
    def get_postgres_async_session(self) -> AsyncSession:
        """获取PostgreSQL异步会话"""
        if self._postgres_async_session is None:
            self.init_postgres()
        return self._postgres_async_session()
    
    @property
    def redis(self) -> redis.Redis:
        """获取Redis客户端"""
        if self._redis_client is None:
            self.init_redis()
        return self._redis_client
    
    @property
    def mongodb(self):
        """获取MongoDB数据库"""
        if self._mongo_db is None:
            self.init_mongodb()
        return self._mongo_db
    
    def create_tables(self) -> None:
        """创建数据库表"""
        if self._postgres_engine is None:
            self.init_postgres()
        
        try:
            Base.metadata.create_all(bind=self._postgres_engine)
            logger.info("数据库表创建完成")
        except Exception as e:
            logger.error(f"创建数据库表失败: {e}")
            raise DatabaseError(f"创建数据库表失败: {e}")
    
    def close_all(self) -> None:
        """关闭所有数据库连接"""
        if self._postgres_engine:
            self._postgres_engine.dispose()
        
        if self._postgres_async_engine:
            asyncio.create_task(self._postgres_async_engine.dispose())
        
        if self._redis_client:
            self._redis_client.close()
        
        if self._mongo_client:
            self._mongo_client.close()
        
        logger.info("所有数据库连接已关闭")


# 全局数据库管理器实例
db_manager = DatabaseManager()
