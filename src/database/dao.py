"""
数据访问层 (DAO)
===============

提供对各种数据模型的CRUD操作。
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, and_, or_
from src.core.database import db_manager
from src.core.logger import get_logger
from src.models import *
from src.models.load import LoadType
from src.models.storage import BatteryType, BatteryStatus
from src.models.grid import PriceType

logger = get_logger(__name__)


class BaseDAO:
    """基础数据访问对象"""
    
    def __init__(self, model_class):
        self.model_class = model_class
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        return db_manager.get_postgres_session()
    
    def create(self, **kwargs) -> Any:
        """创建新记录"""
        session = self.get_session()
        try:
            obj = self.model_class(**kwargs)
            session.add(obj)
            session.commit()
            session.refresh(obj)
            return obj
        except Exception as e:
            session.rollback()
            logger.error(f"创建记录失败: {e}")
            raise
        finally:
            session.close()
    
    def get_by_id(self, obj_id: int) -> Optional[Any]:
        """根据ID获取记录"""
        session = self.get_session()
        try:
            return session.query(self.model_class).filter(
                self.model_class.id == obj_id
            ).first()
        finally:
            session.close()
    
    def get_all(self, limit: int = 100, offset: int = 0) -> List[Any]:
        """获取所有记录"""
        session = self.get_session()
        try:
            return session.query(self.model_class).offset(offset).limit(limit).all()
        finally:
            session.close()
    
    def update(self, obj_id: int, **kwargs) -> Optional[Any]:
        """更新记录"""
        session = self.get_session()
        try:
            obj = session.query(self.model_class).filter(
                self.model_class.id == obj_id
            ).first()
            if obj:
                for key, value in kwargs.items():
                    setattr(obj, key, value)
                session.commit()
                session.refresh(obj)
            return obj
        except Exception as e:
            session.rollback()
            logger.error(f"更新记录失败: {e}")
            raise
        finally:
            session.close()
    
    def delete(self, obj_id: int) -> bool:
        """删除记录"""
        session = self.get_session()
        try:
            obj = session.query(self.model_class).filter(
                self.model_class.id == obj_id
            ).first()
            if obj:
                session.delete(obj)
                session.commit()
                return True
            return False
        except Exception as e:
            session.rollback()
            logger.error(f"删除记录失败: {e}")
            raise
        finally:
            session.close()


class SolarDAO(BaseDAO):
    """光伏数据访问对象"""
    
    def __init__(self):
        super().__init__(SolarPanel)
    
    def get_solar_data(self, panel_id: int, start_time: datetime, 
                      end_time: datetime) -> List[SolarData]:
        """获取光伏数据"""
        session = self.get_session()
        try:
            return session.query(SolarData).filter(
                and_(
                    SolarData.panel_id == panel_id,
                    SolarData.timestamp >= start_time,
                    SolarData.timestamp <= end_time
                )
            ).order_by(SolarData.timestamp).all()
        finally:
            session.close()
    
    def get_latest_data(self, panel_id: int) -> Optional[SolarData]:
        """获取最新的光伏数据"""
        session = self.get_session()
        try:
            return session.query(SolarData).filter(
                SolarData.panel_id == panel_id
            ).order_by(desc(SolarData.timestamp)).first()
        finally:
            session.close()
    
    def add_solar_data(self, panel_id: int, **kwargs) -> SolarData:
        """添加光伏数据"""
        session = self.get_session()
        try:
            data = SolarData(panel_id=panel_id, **kwargs)
            session.add(data)
            session.commit()
            session.refresh(data)
            return data
        except Exception as e:
            session.rollback()
            logger.error(f"添加光伏数据失败: {e}")
            raise
        finally:
            session.close()


class BatteryDAO(BaseDAO):
    """电池数据访问对象"""
    
    def __init__(self):
        super().__init__(Battery)
    
    def get_battery_data(self, battery_id: int, start_time: datetime, 
                        end_time: datetime) -> List[BatteryData]:
        """获取电池数据"""
        session = self.get_session()
        try:
            return session.query(BatteryData).filter(
                and_(
                    BatteryData.battery_id == battery_id,
                    BatteryData.timestamp >= start_time,
                    BatteryData.timestamp <= end_time
                )
            ).order_by(BatteryData.timestamp).all()
        finally:
            session.close()
    
    def get_current_soc(self, battery_id: int) -> Optional[float]:
        """获取当前SOC"""
        session = self.get_session()
        try:
            latest_data = session.query(BatteryData).filter(
                BatteryData.battery_id == battery_id
            ).order_by(desc(BatteryData.timestamp)).first()
            return latest_data.soc if latest_data else None
        finally:
            session.close()
    
    def add_battery_data(self, battery_id: int, **kwargs) -> BatteryData:
        """添加电池数据"""
        session = self.get_session()
        try:
            data = BatteryData(battery_id=battery_id, **kwargs)
            session.add(data)
            session.commit()
            session.refresh(data)
            return data
        except Exception as e:
            session.rollback()
            logger.error(f"添加电池数据失败: {e}")
            raise
        finally:
            session.close()


class LoadDAO(BaseDAO):
    """负荷数据访问对象"""
    
    def __init__(self):
        super().__init__(Load)
    
    def get_load_data(self, load_id: int, start_time: datetime, 
                     end_time: datetime) -> List[LoadData]:
        """获取负荷数据"""
        session = self.get_session()
        try:
            return session.query(LoadData).filter(
                and_(
                    LoadData.load_id == load_id,
                    LoadData.timestamp >= start_time,
                    LoadData.timestamp <= end_time
                )
            ).order_by(LoadData.timestamp).all()
        finally:
            session.close()
    
    def get_loads_by_type(self, load_type: LoadType) -> List[Load]:
        """根据类型获取负荷设备"""
        session = self.get_session()
        try:
            return session.query(Load).filter(
                Load.load_type == load_type
            ).all()
        finally:
            session.close()


class GridDAO(BaseDAO):
    """电网数据访问对象"""
    
    def __init__(self):
        super().__init__(GridData)
    
    def get_electricity_prices(self, start_date: datetime, 
                             end_date: datetime) -> List[ElectricityPrice]:
        """获取电价数据"""
        session = self.get_session()
        try:
            return session.query(ElectricityPrice).filter(
                and_(
                    ElectricityPrice.date >= start_date,
                    ElectricityPrice.date <= end_date
                )
            ).order_by(ElectricityPrice.date, ElectricityPrice.hour).all()
        finally:
            session.close()
    
    def get_current_price(self) -> Optional[ElectricityPrice]:
        """获取当前电价"""
        session = self.get_session()
        try:
            now = datetime.now()
            current_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
            current_hour = now.hour
            
            return session.query(ElectricityPrice).filter(
                and_(
                    ElectricityPrice.date == current_date,
                    ElectricityPrice.hour == current_hour
                )
            ).first()
        finally:
            session.close()


class WeatherDAO(BaseDAO):
    """天气数据访问对象"""
    
    def __init__(self):
        super().__init__(WeatherData)
    
    def get_weather_data(self, start_time: datetime, 
                        end_time: datetime) -> List[WeatherData]:
        """获取天气数据"""
        session = self.get_session()
        try:
            return session.query(WeatherData).filter(
                and_(
                    WeatherData.timestamp >= start_time,
                    WeatherData.timestamp <= end_time
                )
            ).order_by(WeatherData.timestamp).all()
        finally:
            session.close()
    
    def get_latest_weather(self) -> Optional[WeatherData]:
        """获取最新天气数据"""
        session = self.get_session()
        try:
            return session.query(WeatherData).order_by(
                desc(WeatherData.timestamp)
            ).first()
        finally:
            session.close()


class OptimizationDAO(BaseDAO):
    """优化数据访问对象"""
    
    def __init__(self):
        super().__init__(OptimizationResult)
    
    def get_optimization_history(self, optimization_id: str) -> List[OptimizationHistory]:
        """获取优化历史"""
        session = self.get_session()
        try:
            result = session.query(OptimizationResult).filter(
                OptimizationResult.optimization_id == optimization_id
            ).first()
            
            if result:
                return session.query(OptimizationHistory).filter(
                    OptimizationHistory.optimization_result_id == result.id
                ).order_by(OptimizationHistory.time_step).all()
            return []
        finally:
            session.close()
    
    def get_recent_results(self, limit: int = 10) -> List[OptimizationResult]:
        """获取最近的优化结果"""
        session = self.get_session()
        try:
            return session.query(OptimizationResult).order_by(
                desc(OptimizationResult.created_at)
            ).limit(limit).all()
        finally:
            session.close()
