"""
光伏系统数据模型
===============

定义光伏板、光伏数据和光伏预测的数据模型。
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import Column, Integer, Float, String, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from .base import DeviceModel, DataModel, ForecastModel


class SolarPanel(DeviceModel):
    """光伏板设备模型"""
    
    __tablename__ = "solar_panels"
    
    # 物理参数
    area = Column(Float, nullable=False, comment="面积(平方米)")
    efficiency = Column(Float, nullable=False, comment="转换效率")
    rated_power = Column(Float, nullable=False, comment="额定功率(kW)")
    
    # 安装参数
    orientation = Column(Float, comment="朝向角度(度)")
    tilt = Column(Float, comment="倾斜角度(度)")
    latitude = Column(Float, comment="纬度")
    longitude = Column(Float, comment="经度")
    altitude = Column(Float, comment="海拔(米)")
    
    # 环境参数
    shading_factor = Column(Float, default=1.0, comment="遮挡系数")
    soiling_factor = Column(Float, default=0.95, comment="污染系数")
    temperature_coefficient = Column(Float, default=-0.004, comment="温度系数")
    
    # 逆变器参数
    inverter_efficiency = Column(Float, default=0.95, comment="逆变器效率")
    inverter_capacity = Column(Float, comment="逆变器容量(kW)")
    
    # 关联关系
    solar_data = relationship("SolarData", back_populates="panel")
    solar_forecasts = relationship("SolarForecast", back_populates="panel")


class SolarData(DataModel):
    """光伏实时数据模型"""
    
    __tablename__ = "solar_data"
    
    # 外键关联
    panel_id = Column(Integer, ForeignKey("solar_panels.id"), nullable=False)
    
    # 发电数据
    power_output = Column(Float, comment="实时功率输出(kW)")
    energy_today = Column(Float, comment="今日发电量(kWh)")
    energy_total = Column(Float, comment="累计发电量(kWh)")
    
    # 环境数据
    irradiance = Column(Float, comment="太阳辐照度(W/m²)")
    panel_temperature = Column(Float, comment="组件温度(°C)")
    ambient_temperature = Column(Float, comment="环境温度(°C)")
    wind_speed = Column(Float, comment="风速(m/s)")
    humidity = Column(Float, comment="湿度(%)")
    
    # 电气参数
    dc_voltage = Column(Float, comment="直流电压(V)")
    dc_current = Column(Float, comment="直流电流(A)")
    ac_voltage = Column(Float, comment="交流电压(V)")
    ac_current = Column(Float, comment="交流电流(A)")
    frequency = Column(Float, comment="频率(Hz)")
    power_factor = Column(Float, comment="功率因数")
    
    # 效率指标
    conversion_efficiency = Column(Float, comment="转换效率")
    performance_ratio = Column(Float, comment="性能比")
    
    # 状态信息
    inverter_status = Column(String(50), comment="逆变器状态")
    fault_codes = Column(Text, comment="故障代码")
    
    # 关联关系
    panel = relationship("SolarPanel", back_populates="solar_data")


class SolarForecast(ForecastModel):
    """光伏发电预测模型"""
    
    __tablename__ = "solar_forecasts"
    
    # 外键关联
    panel_id = Column(Integer, ForeignKey("solar_panels.id"), nullable=False)
    
    # 预测结果
    predicted_power = Column(Float, comment="预测功率(kW)")
    predicted_energy = Column(Float, comment="预测发电量(kWh)")
    predicted_irradiance = Column(Float, comment="预测辐照度(W/m²)")
    
    # 预测区间
    power_lower_bound = Column(Float, comment="功率预测下界")
    power_upper_bound = Column(Float, comment="功率预测上界")
    
    # 天气预测输入
    weather_forecast_id = Column(Integer, comment="天气预测ID")
    cloud_cover = Column(Float, comment="云量(%)")
    visibility = Column(Float, comment="能见度(km)")
    
    # 模型特定参数
    clear_sky_irradiance = Column(Float, comment="晴空辐照度")
    atmospheric_transmittance = Column(Float, comment="大气透射率")
    
    # 关联关系
    panel = relationship("SolarPanel", back_populates="solar_forecasts")
