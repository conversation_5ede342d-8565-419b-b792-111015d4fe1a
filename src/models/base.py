"""
基础数据模型
===========

定义所有数据模型的基类和通用字段。
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import Column, Integer, DateTime, String, Float, Boolean, Text
from sqlalchemy.ext.declarative import declared_attr
from src.core.database import Base


class TimestampMixin:
    """时间戳混入类"""
    
    @declared_attr
    def created_at(cls):
        return Column(DateTime, default=datetime.utcnow, nullable=False)
    
    @declared_attr
    def updated_at(cls):
        return Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)


class BaseModel(Base, TimestampMixin):
    """基础模型类"""
    
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(id={self.id})>"


class DeviceModel(BaseModel):
    """设备基础模型"""
    
    __abstract__ = True
    
    name = Column(String(100), nullable=False, comment="设备名称")
    description = Column(Text, comment="设备描述")
    location = Column(String(200), comment="设备位置")
    is_active = Column(Boolean, default=True, comment="是否激活")
    manufacturer = Column(String(100), comment="制造商")
    model = Column(String(100), comment="型号")
    serial_number = Column(String(100), unique=True, comment="序列号")
    installation_date = Column(DateTime, comment="安装日期")
    warranty_expiry = Column(DateTime, comment="保修到期日期")


class DataModel(BaseModel):
    """数据基础模型"""
    
    __abstract__ = True
    
    timestamp = Column(DateTime, nullable=False, index=True, comment="时间戳")
    device_id = Column(Integer, nullable=False, index=True, comment="设备ID")
    
    # 数据质量标识
    quality_flag = Column(String(20), default="good", comment="数据质量标识")
    source = Column(String(50), comment="数据来源")


class ForecastModel(BaseModel):
    """预测基础模型"""
    
    __abstract__ = True
    
    forecast_time = Column(DateTime, nullable=False, index=True, comment="预测时间")
    target_time = Column(DateTime, nullable=False, index=True, comment="目标时间")
    model_name = Column(String(100), comment="模型名称")
    model_version = Column(String(50), comment="模型版本")
    confidence = Column(Float, comment="置信度")
    
    # 预测元数据
    forecast_horizon = Column(Integer, comment="预测时长(小时)")
    input_features = Column(Text, comment="输入特征(JSON)")
    model_params = Column(Text, comment="模型参数(JSON)")
