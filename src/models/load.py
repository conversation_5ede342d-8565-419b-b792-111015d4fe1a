"""
负荷系统数据模型
===============

定义负荷设备、负荷数据和负荷预测的数据模型。
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import Column, Integer, Float, String, DateTime, ForeignKey, Text, Enum, Boolean
from sqlalchemy.orm import relationship
import enum
from .base import DeviceModel, DataModel, ForecastModel, BaseModel


class LoadType(enum.Enum):
    """负荷类型枚举"""
    HVAC = "hvac"  # 空调
    LIGHTING = "lighting"  # 照明
    PRODUCTION = "production"  # 生产设备
    EV_CHARGING = "ev_charging"  # 电动汽车充电
    OFFICE_EQUIPMENT = "office_equipment"  # 办公设备
    OTHER = "other"  # 其他


class LoadPriority(enum.Enum):
    """负荷优先级枚举"""
    CRITICAL = "critical"  # 关键负荷
    HIGH = "high"  # 高优先级
    MEDIUM = "medium"  # 中等优先级
    LOW = "low"  # 低优先级
    FLEXIBLE = "flexible"  # 可调节负荷


class Load(DeviceModel):
    """负荷设备模型"""
    
    __tablename__ = "loads"
    
    # 基本参数
    load_type = Column(Enum(LoadType), nullable=False, comment="负荷类型")
    priority = Column(Enum(LoadPriority), nullable=False, comment="优先级")
    rated_power = Column(Float, nullable=False, comment="额定功率(kW)")
    
    # 运行特性
    min_power = Column(Float, default=0.0, comment="最小功率(kW)")
    max_power = Column(Float, comment="最大功率(kW)")
    power_factor = Column(Float, default=0.9, comment="功率因数")
    
    # 调节特性
    flexibility = Column(Float, default=0.0, comment="灵活性系数(0-1)")
    min_on_time = Column(Float, comment="最小开机时间(小时)")
    min_off_time = Column(Float, comment="最小关机时间(小时)")
    ramp_up_rate = Column(Float, comment="升功率速率(kW/min)")
    ramp_down_rate = Column(Float, comment="降功率速率(kW/min)")
    
    # 调度参数
    can_shed = Column(Boolean, default=False, comment="是否可切除")
    can_shift = Column(Boolean, default=False, comment="是否可转移")
    shift_duration = Column(Float, comment="可转移时长(小时)")
    
    # 成本参数
    interruption_cost = Column(Float, comment="中断成本(元/kWh)")
    delay_cost = Column(Float, comment="延迟成本(元/小时)")
    
    # 环境参数
    temperature_sensitive = Column(Boolean, default=False, comment="是否温度敏感")
    optimal_temp_min = Column(Float, comment="最佳温度下限(°C)")
    optimal_temp_max = Column(Float, comment="最佳温度上限(°C)")
    
    # 关联关系
    load_data = relationship("LoadData", back_populates="load")
    load_forecasts = relationship("LoadForecast", back_populates="load")


class LoadData(DataModel):
    """负荷实时数据模型"""
    
    __tablename__ = "load_data"
    
    # 外键关联
    load_id = Column(Integer, ForeignKey("loads.id"), nullable=False)
    
    # 功率数据
    active_power = Column(Float, comment="有功功率(kW)")
    reactive_power = Column(Float, comment="无功功率(kVar)")
    apparent_power = Column(Float, comment="视在功率(kVA)")
    power_factor = Column(Float, comment="功率因数")
    
    # 电气参数
    voltage = Column(Float, comment="电压(V)")
    current = Column(Float, comment="电流(A)")
    frequency = Column(Float, comment="频率(Hz)")
    
    # 能耗数据
    energy_consumption = Column(Float, comment="累计耗电量(kWh)")
    daily_consumption = Column(Float, comment="日耗电量(kWh)")
    monthly_consumption = Column(Float, comment="月耗电量(kWh)")
    
    # 运行状态
    is_online = Column(Boolean, comment="是否在线")
    is_running = Column(Boolean, comment="是否运行")
    load_factor = Column(Float, comment="负荷率")
    utilization_rate = Column(Float, comment="利用率")
    
    # 环境数据
    ambient_temperature = Column(Float, comment="环境温度(°C)")
    humidity = Column(Float, comment="湿度(%)")
    occupancy = Column(Integer, comment="人员数量")
    
    # 控制状态
    setpoint = Column(Float, comment="设定值")
    control_mode = Column(String(50), comment="控制模式")
    schedule_status = Column(String(50), comment="计划状态")
    
    # 效率指标
    efficiency = Column(Float, comment="效率")
    performance_index = Column(Float, comment="性能指数")
    
    # 告警信息
    alarm_status = Column(String(50), comment="告警状态")
    fault_codes = Column(Text, comment="故障代码")
    
    # 关联关系
    load = relationship("Load", back_populates="load_data")


class LoadForecast(ForecastModel):
    """负荷预测模型"""
    
    __tablename__ = "load_forecasts"
    
    # 外键关联
    load_id = Column(Integer, ForeignKey("loads.id"), nullable=False)
    
    # 预测结果
    predicted_power = Column(Float, comment="预测功率(kW)")
    predicted_energy = Column(Float, comment="预测能耗(kWh)")
    
    # 预测区间
    power_lower_bound = Column(Float, comment="功率预测下界")
    power_upper_bound = Column(Float, comment="功率预测上界")
    
    # 负荷特征
    base_load = Column(Float, comment="基础负荷(kW)")
    peak_load = Column(Float, comment="峰值负荷(kW)")
    load_pattern = Column(String(50), comment="负荷模式")
    
    # 影响因素
    weather_impact = Column(Float, comment="天气影响系数")
    schedule_impact = Column(Float, comment="计划影响系数")
    occupancy_impact = Column(Float, comment="人员影响系数")
    
    # 调节潜力
    shed_potential = Column(Float, comment="可切除潜力(kW)")
    shift_potential = Column(Float, comment="可转移潜力(kW)")
    flexibility_window = Column(Text, comment="灵活性时间窗(JSON)")
    
    # 成本预测
    electricity_cost = Column(Float, comment="预测电费(元)")
    demand_charge = Column(Float, comment="需量电费(元)")
    
    # 关联关系
    load = relationship("Load", back_populates="load_forecasts")


class LoadSchedule(BaseModel):
    """负荷调度计划模型"""
    
    __tablename__ = "load_schedules"
    
    # 基本信息
    load_id = Column(Integer, ForeignKey("loads.id"), nullable=False)
    schedule_name = Column(String(100), comment="计划名称")
    schedule_type = Column(String(50), comment="计划类型")
    
    # 时间信息
    start_time = Column(DateTime, comment="开始时间")
    end_time = Column(DateTime, comment="结束时间")
    duration = Column(Float, comment="持续时间(小时)")
    
    # 功率计划
    target_power = Column(Float, comment="目标功率(kW)")
    min_power = Column(Float, comment="最小功率(kW)")
    max_power = Column(Float, comment="最大功率(kW)")
    
    # 执行状态
    status = Column(String(50), comment="执行状态")
    completion_rate = Column(Float, comment="完成率")
    
    # 优先级和约束
    priority = Column(Integer, comment="优先级")
    constraints = Column(Text, comment="约束条件(JSON)")
    
    # 关联关系
    load = relationship("Load")
