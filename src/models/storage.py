"""
储能系统数据模型
===============

定义电池、储能数据和储能预测的数据模型。
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import Column, Integer, Float, String, DateTime, ForeignKey, Text, Enum
from sqlalchemy.orm import relationship
import enum
from .base import DeviceModel, DataModel, ForecastModel


class BatteryType(enum.Enum):
    """电池类型枚举"""
    LITHIUM_ION = "lithium_ion"
    LEAD_ACID = "lead_acid"
    FLOW_BATTERY = "flow_battery"
    SODIUM_ION = "sodium_ion"


class BatteryStatus(enum.Enum):
    """电池状态枚举"""
    IDLE = "idle"
    CHARGING = "charging"
    DISCHARGING = "discharging"
    FAULT = "fault"
    MAINTENANCE = "maintenance"


class Battery(DeviceModel):
    """电池设备模型"""
    
    __tablename__ = "batteries"
    
    # 基本参数
    battery_type = Column(Enum(BatteryType), nullable=False, comment="电池类型")
    nominal_capacity = Column(Float, nullable=False, comment="标称容量(kWh)")
    usable_capacity = Column(Float, nullable=False, comment="可用容量(kWh)")
    nominal_voltage = Column(Float, nullable=False, comment="标称电压(V)")
    
    # 功率参数
    max_charge_power = Column(Float, nullable=False, comment="最大充电功率(kW)")
    max_discharge_power = Column(Float, nullable=False, comment="最大放电功率(kW)")
    
    # 效率参数
    charge_efficiency = Column(Float, default=0.95, comment="充电效率")
    discharge_efficiency = Column(Float, default=0.95, comment="放电效率")
    round_trip_efficiency = Column(Float, comment="往返效率")
    
    # SOC限制
    min_soc = Column(Float, default=0.1, comment="最小SOC")
    max_soc = Column(Float, default=0.9, comment="最大SOC")
    
    # 寿命参数
    cycle_life = Column(Integer, comment="循环寿命(次)")
    calendar_life = Column(Integer, comment="日历寿命(年)")
    depth_of_discharge = Column(Float, default=0.8, comment="放电深度")
    
    # 温度参数
    operating_temp_min = Column(Float, comment="最低工作温度(°C)")
    operating_temp_max = Column(Float, comment="最高工作温度(°C)")
    optimal_temp_min = Column(Float, comment="最佳温度下限(°C)")
    optimal_temp_max = Column(Float, comment="最佳温度上限(°C)")
    
    # 安全参数
    max_cell_voltage = Column(Float, comment="最大单体电压(V)")
    min_cell_voltage = Column(Float, comment="最小单体电压(V)")
    max_temperature = Column(Float, comment="最高温度(°C)")
    
    # 关联关系
    battery_data = relationship("BatteryData", back_populates="battery")
    battery_forecasts = relationship("BatteryForecast", back_populates="battery")


class BatteryData(DataModel):
    """电池实时数据模型"""
    
    __tablename__ = "battery_data"
    
    # 外键关联
    battery_id = Column(Integer, ForeignKey("batteries.id"), nullable=False)
    
    # 状态数据
    status = Column(Enum(BatteryStatus), comment="电池状态")
    soc = Column(Float, comment="荷电状态(%)")
    soh = Column(Float, comment="健康状态(%)")
    
    # 电气参数
    voltage = Column(Float, comment="电压(V)")
    current = Column(Float, comment="电流(A)")
    power = Column(Float, comment="功率(kW)")
    energy = Column(Float, comment="能量(kWh)")
    
    # 温度数据
    temperature = Column(Float, comment="电池温度(°C)")
    max_cell_temp = Column(Float, comment="最高单体温度(°C)")
    min_cell_temp = Column(Float, comment="最低单体温度(°C)")
    avg_cell_temp = Column(Float, comment="平均单体温度(°C)")
    
    # 单体电压
    max_cell_voltage = Column(Float, comment="最高单体电压(V)")
    min_cell_voltage = Column(Float, comment="最低单体电压(V)")
    avg_cell_voltage = Column(Float, comment="平均单体电压(V)")
    voltage_difference = Column(Float, comment="电压差(V)")
    
    # 累计数据
    total_charge_energy = Column(Float, comment="累计充电量(kWh)")
    total_discharge_energy = Column(Float, comment="累计放电量(kWh)")
    cycle_count = Column(Integer, comment="循环次数")
    
    # 效率数据
    charge_efficiency = Column(Float, comment="实时充电效率")
    discharge_efficiency = Column(Float, comment="实时放电效率")
    
    # 预估数据
    remaining_capacity = Column(Float, comment="剩余容量(kWh)")
    time_to_full = Column(Float, comment="充满时间(小时)")
    time_to_empty = Column(Float, comment="放空时间(小时)")
    
    # 告警信息
    alarm_codes = Column(Text, comment="告警代码")
    fault_codes = Column(Text, comment="故障代码")
    
    # 关联关系
    battery = relationship("Battery", back_populates="battery_data")


class BatteryForecast(ForecastModel):
    """电池状态预测模型"""
    
    __tablename__ = "battery_forecasts"
    
    # 外键关联
    battery_id = Column(Integer, ForeignKey("batteries.id"), nullable=False)
    
    # 预测结果
    predicted_soc = Column(Float, comment="预测SOC(%)")
    predicted_power = Column(Float, comment="预测功率(kW)")
    predicted_energy = Column(Float, comment="预测能量(kWh)")
    
    # 充放电策略
    charge_schedule = Column(Text, comment="充电计划(JSON)")
    discharge_schedule = Column(Text, comment="放电计划(JSON)")
    
    # 优化目标
    cost_savings = Column(Float, comment="成本节约(元)")
    revenue_potential = Column(Float, comment="收益潜力(元)")
    
    # 约束条件
    power_constraints = Column(Text, comment="功率约束(JSON)")
    energy_constraints = Column(Text, comment="能量约束(JSON)")
    
    # 预测区间
    soc_lower_bound = Column(Float, comment="SOC预测下界")
    soc_upper_bound = Column(Float, comment="SOC预测上界")
    
    # 关联关系
    battery = relationship("Battery", back_populates="battery_forecasts")
