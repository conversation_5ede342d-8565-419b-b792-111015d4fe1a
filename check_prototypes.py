#!/usr/bin/env python3
"""
VPP-AI 原型界面完成度检查脚本
=============================

检查原型界面的完成情况和内容质量
"""

import os
import re
from pathlib import Path

def check_page_content(file_path):
    """检查页面内容是否已完善"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否还有placeholder
        has_placeholder = '🚧 此页面正在开发中，敬请期待...' in content
        
        # 检查是否有具体内容
        has_real_content = any([
            'grid-template-columns' in content and 'feature-card' in content,
            'background:' in content and 'border-radius:' in content,
            'display: flex' in content,
            'margin-bottom:' in content and 'padding:' in content
        ])
        
        # 计算内容丰富度
        content_indicators = [
            '📊' in content,  # 图表
            '📈' in content,  # 趋势
            '💰' in content,  # 财务
            '⚡' in content,  # 电力
            '🔋' in content,  # 储能
            '🌞' in content,  # 光伏
            'kW' in content or 'kWh' in content,  # 电力单位
            '%' in content,   # 百分比
            '元' in content or '¥' in content,  # 货币
            'button' in content,  # 交互元素
            'input' in content,   # 输入元素
        ]
        
        content_score = sum(content_indicators) / len(content_indicators)
        
        return {
            'has_placeholder': has_placeholder,
            'has_real_content': has_real_content,
            'content_score': content_score,
            'file_size': len(content)
        }
        
    except Exception as e:
        return {
            'has_placeholder': True,
            'has_real_content': False,
            'content_score': 0,
            'file_size': 0,
            'error': str(e)
        }

def main():
    """主函数"""
    base_dir = Path("原型图")
    
    if not base_dir.exists():
        print("❌ 原型图目录不存在")
        return
    
    print("🔍 检查VPP-AI原型界面完成情况...")
    print("=" * 60)
    
    total_files = 0
    completed_files = 0
    high_quality_files = 0
    
    modules = {}
    
    # 遍历所有HTML文件
    for html_file in base_dir.rglob("*.html"):
        if html_file.name == 'index.html' and html_file.parent == base_dir:
            continue  # 跳过主导航页面
            
        total_files += 1
        relative_path = html_file.relative_to(base_dir)
        module_name = relative_path.parts[0] if len(relative_path.parts) > 1 else 'root'
        
        if module_name not in modules:
            modules[module_name] = {
                'total': 0,
                'completed': 0,
                'high_quality': 0,
                'files': []
            }
        
        modules[module_name]['total'] += 1
        
        # 检查页面内容
        result = check_page_content(html_file)
        
        status = "🚧 开发中"
        if not result['has_placeholder'] and result['has_real_content']:
            completed_files += 1
            modules[module_name]['completed'] += 1
            status = "✅ 已完成"
            
            if result['content_score'] > 0.6:
                high_quality_files += 1
                modules[module_name]['high_quality'] += 1
                status = "🌟 高质量"
        
        modules[module_name]['files'].append({
            'name': html_file.name,
            'status': status,
            'score': result['content_score'],
            'size': result['file_size']
        })
    
    # 打印总体统计
    print(f"📊 总体统计:")
    print(f"   总页面数: {total_files}")
    print(f"   已完成页面: {completed_files} ({completed_files/total_files*100:.1f}%)")
    print(f"   高质量页面: {high_quality_files} ({high_quality_files/total_files*100:.1f}%)")
    print(f"   待完善页面: {total_files - completed_files}")
    print()
    
    # 打印各模块统计
    print("📋 各模块完成情况:")
    print("-" * 60)
    
    module_names = {
        'dashboard': '📊 主控制台',
        'solar': '🌞 光伏系统',
        'storage': '🔋 储能系统',
        'load': '⚡ 负荷系统',
        'grid': '🔌 电网交易',
        'weather': '🌤️ 天气系统',
        'optimization': '🧠 智能优化',
        'digital_twin': '🏭 数字孪生',
        'system': '⚙️ 系统管理',
        'reports': '📈 报表分析'
    }
    
    for module, data in modules.items():
        if module == 'root':
            continue
            
        module_display = module_names.get(module, f"📁 {module}")
        completion_rate = data['completed'] / data['total'] * 100
        quality_rate = data['high_quality'] / data['total'] * 100
        
        print(f"{module_display}")
        print(f"   总页面: {data['total']} | 已完成: {data['completed']} ({completion_rate:.1f}%) | 高质量: {data['high_quality']} ({quality_rate:.1f}%)")
        
        # 显示页面状态
        for file_info in sorted(data['files'], key=lambda x: x['score'], reverse=True):
            print(f"   {file_info['status']} {file_info['name']} (质量: {file_info['score']:.1f}, 大小: {file_info['size']}字节)")
        print()
    
    # 生成改进建议
    print("💡 改进建议:")
    print("-" * 60)
    
    if completed_files < total_files * 0.8:
        print("🎯 优先完善核心功能页面，如监控、控制、分析等关键界面")
    
    if high_quality_files < total_files * 0.5:
        print("🎨 提升页面内容质量，添加更多具体的数据、图表和交互元素")
    
    if completed_files > total_files * 0.8:
        print("🎉 原型系统完成度很高！可以考虑添加更多细节和交互功能")
    
    print("\n✅ 检查完成！")

if __name__ == "__main__":
    main()
