#!/usr/bin/env python3
"""
VPP-AI 原型界面批量完善脚本
=============================

为所有原型界面添加具体的、有意义的内容
"""

import os
import re
from pathlib import Path

# 定义更多页面的具体内容
ADDITIONAL_CONTENTS = {
    'dashboard/overview.html': '''
        <!-- 系统总览仪表板 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
            <div class="feature-card" style="text-align: center; border-left: 4px solid #667eea;">
                <div style="font-size: 32px; font-weight: bold; color: #667eea; margin-bottom: 10px;">98.5%</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">系统可用率</div>
                <div style="font-size: 12px; color: #27ae60;">✅ 运行稳定</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #f39c12;">
                <div style="font-size: 32px; font-weight: bold; color: #f39c12; margin-bottom: 10px;">1,248 kWh</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">今日发电量</div>
                <div style="font-size: 12px; color: #27ae60;">📈 +8.3% 较昨日</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #74b9ff;">
                <div style="font-size: 32px; font-weight: bold; color: #74b9ff; margin-bottom: 10px;">18.5%</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">储能SOC</div>
                <div style="font-size: 12px; color: #e74c3c;">⚠️ 低电量告警</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #00b894;">
                <div style="font-size: 32px; font-weight: bold; color: #00b894; margin-bottom: 10px;">89.2 kW</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">当前负荷</div>
                <div style="font-size: 12px; color: #00b894;">📊 负荷率 67%</div>
            </div>
        </div>
        
        <!-- 系统架构图 -->
        <div class="feature-card" style="margin-bottom: 30px;">
            <div class="feature-title">🏗️ 系统架构总览</div>
            <div style="height: 400px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #6c757d; margin-top: 15px; position: relative;">
                <div style="position: absolute; top: 50px; left: 100px; width: 120px; height: 80px; background: #ffeaa7; border: 2px solid #fdcb6e; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #2c3e50;">🌞<br>光伏系统<br>156.8kW</div>
                <div style="position: absolute; top: 50px; right: 100px; width: 120px; height: 80px; background: #a8e6cf; border: 2px solid #00b894; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #2c3e50;">⚡<br>负荷系统<br>89.2kW</div>
                <div style="position: absolute; bottom: 50px; left: 100px; width: 120px; height: 80px; background: #dde5ff; border: 2px solid #74b9ff; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #2c3e50;">🔋<br>储能系统<br>18.5%SOC</div>
                <div style="position: absolute; bottom: 50px; right: 100px; width: 120px; height: 80px; background: #e8d5ff; border: 2px solid #a29bfe; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #2c3e50;">🔌<br>电网交易<br>¥2,456</div>
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 100px; height: 100px; background: #ff9ff3; border: 3px solid #fd79a8; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #2c3e50; text-align: center;">🧠<br>智能<br>控制中心</div>
                <!-- 连接线 -->
                <svg style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none;">
                    <line x1="160" y1="90" x2="300" y2="200" stroke="#667eea" stroke-width="3" stroke-dasharray="5,5"/>
                    <line x1="440" y1="90" x2="400" y2="200" stroke="#667eea" stroke-width="3" stroke-dasharray="5,5"/>
                    <line x1="160" y1="290" x2="300" y2="250" stroke="#667eea" stroke-width="3" stroke-dasharray="5,5"/>
                    <line x1="440" y1="290" x2="400" y2="250" stroke="#667eea" stroke-width="3" stroke-dasharray="5,5"/>
                </svg>
            </div>
        </div>
    ''',
    
    'solar/efficiency.html': '''
        <!-- 效率分析概览 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">
            <div class="feature-card" style="text-align: center; border-left: 4px solid #f39c12;">
                <div style="font-size: 28px; font-weight: bold; color: #f39c12; margin-bottom: 5px;">22.1%</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">实际转换效率</div>
                <div style="font-size: 12px; color: #e74c3c;">📉 -0.3% 较理论值</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #00b894;">
                <div style="font-size: 28px; font-weight: bold; color: #00b894; margin-bottom: 5px;">22.4%</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">理论转换效率</div>
                <div style="font-size: 12px; color: #7f8c8d;">🎯 设计目标值</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #6c5ce7;">
                <div style="font-size: 28px; font-weight: bold; color: #6c5ce7; margin-bottom: 5px;">98.7%</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">性能比</div>
                <div style="font-size: 12px; color: #00b894;">✅ 性能良好</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #fd79a8;">
                <div style="font-size: 28px; font-weight: bold; color: #fd79a8; margin-bottom: 5px;">2.3%</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">总损失率</div>
                <div style="font-size: 12px; color: #f39c12;">⚠️ 需要优化</div>
            </div>
        </div>
        
        <!-- 效率分析图表 -->
        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin-bottom: 30px;">
            <div class="feature-card">
                <div class="feature-title">📊 效率变化趋势分析</div>
                <div style="height: 300px; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #6c757d; margin-top: 15px;">
                    📈 光伏系统效率变化曲线
                    <br>显示日、周、月效率变化趋势
                    <br>包含温度、辐照度影响分析
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-title">🔍 损失分析</div>
                <div style="margin-top: 15px;">
                    <div style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span style="font-size: 14px;">温度损失</span>
                            <span style="font-weight: bold; color: #e74c3c;">0.8%</span>
                        </div>
                        <div style="background: #eee; height: 8px; border-radius: 4px;">
                            <div style="background: #e74c3c; height: 8px; width: 35%; border-radius: 4px;"></div>
                        </div>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span style="font-size: 14px;">阴影损失</span>
                            <span style="font-weight: bold; color: #f39c12;">0.5%</span>
                        </div>
                        <div style="background: #eee; height: 8px; border-radius: 4px;">
                            <div style="background: #f39c12; height: 8px; width: 22%; border-radius: 4px;"></div>
                        </div>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span style="font-size: 14px;">灰尘损失</span>
                            <span style="font-weight: bold; color: #74b9ff;">0.6%</span>
                        </div>
                        <div style="background: #eee; height: 8px; border-radius: 4px;">
                            <div style="background: #74b9ff; height: 8px; width: 26%; border-radius: 4px;"></div>
                        </div>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span style="font-size: 14px;">线路损失</span>
                            <span style="font-weight: bold; color: #a29bfe;">0.4%</span>
                        </div>
                        <div style="background: #eee; height: 8px; border-radius: 4px;">
                            <div style="background: #a29bfe; height: 8px; width: 17%; border-radius: 4px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    ''',
    
    'storage/charge_control.html': '''
        <!-- 充放电控制面板 -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
            <div class="feature-card">
                <div class="feature-title">🔋 充电控制</div>
                <div style="margin-top: 15px;">
                    <div style="background: #e8f5e8; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #27ae60; margin-bottom: 10px;">当前状态: 快速充电中</div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>充电功率:</span>
                            <span style="font-weight: bold;">85.2 kW</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>充电电流:</span>
                            <span style="font-weight: bold;">213.0 A</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>预计完成时间:</span>
                            <span style="font-weight: bold;">2小时15分</span>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; font-weight: bold; margin-bottom: 5px;">目标SOC:</label>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <input type="range" min="20" max="100" value="80" style="flex: 1;">
                            <span style="font-weight: bold; min-width: 40px;">80%</span>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; font-weight: bold; margin-bottom: 5px;">充电功率限制:</label>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <input type="range" min="10" max="100" value="85" style="flex: 1;">
                            <span style="font-weight: bold; min-width: 50px;">85 kW</span>
                        </div>
                    </div>
                    
                    <div style="display: flex; gap: 8px;">
                        <button style="flex: 1; background: #27ae60; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">🚀 开始充电</button>
                        <button style="flex: 1; background: #f39c12; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">⏸️ 暂停充电</button>
                    </div>
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-title">⚡ 放电控制</div>
                <div style="margin-top: 15px;">
                    <div style="background: #f8d7da; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #721c24; margin-bottom: 10px;">当前状态: 待机中</div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>放电功率:</span>
                            <span style="font-weight: bold;">0 kW</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>可放电时长:</span>
                            <span style="font-weight: bold;">1.2 小时</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>最低SOC限制:</span>
                            <span style="font-weight: bold;">10%</span>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; font-weight: bold; margin-bottom: 5px;">最低SOC:</label>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <input type="range" min="5" max="30" value="10" style="flex: 1;">
                            <span style="font-weight: bold; min-width: 40px;">10%</span>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; font-weight: bold; margin-bottom: 5px;">放电功率限制:</label>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <input type="range" min="10" max="100" value="75" style="flex: 1;">
                            <span style="font-weight: bold; min-width: 50px;">75 kW</span>
                        </div>
                    </div>
                    
                    <div style="display: flex; gap: 8px;">
                        <button style="flex: 1; background: #e74c3c; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">⚡ 开始放电</button>
                        <button style="flex: 1; background: #95a5a6; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">⏹️ 停止放电</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 充放电策略 -->
        <div class="feature-card">
            <div class="feature-title">🧠 智能充放电策略</div>
            <div style="margin-top: 15px;">
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
                    <div style="background: #e8f5e8; border-radius: 8px; padding: 15px; text-align: center; cursor: pointer; border: 2px solid #27ae60;">
                        <div style="font-size: 20px; margin-bottom: 10px;">💰</div>
                        <div style="font-weight: bold; color: #27ae60; margin-bottom: 5px;">峰谷套利</div>
                        <div style="font-size: 12px; color: #7f8c8d;">谷时充电，峰时放电</div>
                        <div style="font-size: 12px; color: #27ae60; margin-top: 5px;">✅ 当前策略</div>
                    </div>
                    <div style="background: #f8f9fa; border-radius: 8px; padding: 15px; text-align: center; cursor: pointer; border: 2px solid #ddd;">
                        <div style="font-size: 20px; margin-bottom: 10px;">🌞</div>
                        <div style="font-weight: bold; color: #f39c12; margin-bottom: 5px;">光伏优先</div>
                        <div style="font-size: 12px; color: #7f8c8d;">优先存储光伏电量</div>
                        <div style="font-size: 12px; color: #7f8c8d; margin-top: 5px;">点击启用</div>
                    </div>
                    <div style="background: #f8f9fa; border-radius: 8px; padding: 15px; text-align: center; cursor: pointer; border: 2px solid #ddd;">
                        <div style="font-size: 20px; margin-bottom: 10px;">⚡</div>
                        <div style="font-weight: bold; color: #6c5ce7; margin-bottom: 5px;">需求响应</div>
                        <div style="font-size: 12px; color: #7f8c8d;">参与电网需求响应</div>
                        <div style="font-size: 12px; color: #7f8c8d; margin-top: 5px;">点击启用</div>
                    </div>
                </div>
            </div>
        </div>
    '''
}

def update_page_with_content(file_path, content):
    """更新页面内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 查找并替换placeholder部分
        placeholder_pattern = r'<div class="placeholder">.*?</div>\s*<div class="feature-grid">.*?</div>'
        
        if re.search(placeholder_pattern, html_content, re.DOTALL):
            new_content = content.strip()
            html_content = re.sub(placeholder_pattern, new_content, html_content, flags=re.DOTALL)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"✅ 更新页面: {file_path}")
            return True
        else:
            print(f"⚠️  未找到placeholder: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ 更新失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    base_dir = Path("原型图")
    updated_count = 0
    
    print("🚀 开始批量完善原型界面...")
    
    for page_path, content in ADDITIONAL_CONTENTS.items():
        full_path = base_dir / page_path
        if full_path.exists():
            if update_page_with_content(full_path, content):
                updated_count += 1
        else:
            print(f"❌ 文件不存在: {full_path}")
    
    print(f"\n✅ 完成批量更新，共更新了 {updated_count} 个页面")
    print("📊 原型系统现在包含更多具体的、有意义的内容")

if __name__ == "__main__":
    main()
