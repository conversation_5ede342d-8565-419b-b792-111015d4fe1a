# VPP-AI 使用指南

## 快速开始

### 1. 环境准备

确保您的系统已安装：
- Python 3.9+
- Git

### 2. 项目设置

```bash
# 克隆项目（如果需要）
cd /path/to/your/projects
git clone <repository-url> VPP-AI
cd VPP-AI

# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install click loguru pyyaml pydantic sqlalchemy redis pymongo psycopg2-binary
```

### 3. 运行系统

```bash
# 激活虚拟环境（如果还没有激活）
source venv/bin/activate

# 运行主系统
python main.py run

# 查看帮助
python main.py --help

# 运行演示
python demo.py
```

## 命令行工具

### 主要命令

```bash
# 运行系统
python main.py run

# 初始化数据库（需要先配置数据库）
python main.py init-db

# 检查数据库状态
python main.py check-db

# 查看帮助
python main.py --help
```

### 演示脚本

```bash
# 运行完整演示
python demo.py
```

演示脚本将展示：
- 配置系统功能
- 数据模型创建
- 时序数据模拟
- 优化场景演示
- 系统集成状态

## 系统功能

### ✅ 已实现功能

1. **核心架构**
   - 模块化设计
   - 配置管理系统
   - 日志管理系统
   - 异常处理框架

2. **数据模型**
   - 光伏系统模型（设备、数据、预测）
   - 储能系统模型（电池、数据、预测）
   - 负荷系统模型（设备、数据、预测、调度）
   - 电网系统模型（数据、电价、交易、需求响应）
   - 天气系统模型（数据、预测、预警、气候）
   - 优化系统模型（结果、历史、决策、性能）

3. **数据访问层**
   - 基础DAO类
   - 专用DAO类（光伏、储能、负荷、电网、天气、优化）
   - CRUD操作支持
   - 时序数据查询

4. **命令行工具**
   - 系统运行命令
   - 数据库管理命令
   - 状态检查命令

### 🔄 开发中功能

1. **数据库集成**
   - PostgreSQL主数据库
   - Redis缓存系统
   - MongoDB时序数据库

2. **数据验证**
   - 输入数据验证
   - 数据清洗模块

### 📋 待开发功能

1. **AI预测模块**
   - 光伏发电预测
   - 负荷预测
   - 天气数据集成

2. **优化引擎**
   - 储能充放电优化
   - 电网交易策略
   - 多目标优化

3. **数字孪生**
   - 设备建模
   - 实时仿真
   - 场景分析

4. **Web界面**
   - 管理界面
   - 数据可视化
   - 实时监控

## 配置说明

### 主配置文件：config.yaml

系统配置包括：
- 系统基本信息
- 数据库连接配置
- 光伏系统参数
- 储能系统参数
- 负荷系统配置
- 电网配置
- AI模型配置
- 优化算法配置

### 关键配置项

```yaml
# 系统配置
system:
  name: "VPP-AI"
  version: "1.0.0"
  debug: true

# 光伏配置
solar:
  panel_area: 1000.0      # 光伏板面积(m²)
  panel_efficiency: 0.22   # 转换效率
  inverter_capacity: 200.0 # 逆变器容量(kW)

# 储能配置
storage:
  capacity: 500.0          # 电池容量(kWh)
  max_charge_power: 100.0  # 最大充电功率(kW)
  charge_efficiency: 0.95  # 充电效率
```

## 数据模型说明

### 设备模型
- **SolarPanel**: 光伏板设备
- **Battery**: 储能电池设备
- **Load**: 负荷设备

### 数据模型
- **SolarData**: 光伏实时数据
- **BatteryData**: 电池实时数据
- **LoadData**: 负荷实时数据
- **GridData**: 电网实时数据
- **WeatherData**: 天气实时数据

### 预测模型
- **SolarForecast**: 光伏发电预测
- **BatteryForecast**: 电池状态预测
- **LoadForecast**: 负荷预测
- **WeatherForecast**: 天气预测

### 优化模型
- **OptimizationResult**: 优化结果
- **OptimizationHistory**: 优化历史
- **DecisionRecord**: 决策记录
- **PerformanceMetrics**: 性能指标

## 开发指南

### 添加新功能

1. **数据模型**：在 `src/models/` 目录下创建新的模型文件
2. **业务逻辑**：在 `src/services/` 目录下添加服务模块
3. **API接口**：在 `src/api/` 目录下添加路由和接口
4. **数据访问**：在 `src/database/dao.py` 中添加新的DAO类

### 测试

```bash
# 运行演示测试
python demo.py

# 运行系统测试
python main.py run
```

## 故障排除

### 常见问题

1. **模块导入错误**
   - 确保虚拟环境已激活
   - 检查依赖包是否安装完整

2. **配置文件错误**
   - 检查 config.yaml 文件格式
   - 确保配置项正确

3. **数据库连接问题**
   - 确保数据库服务已启动
   - 检查连接配置

### 日志查看

系统日志保存在 `logs/` 目录下：
- `vpp_ai.log`: 主要系统日志
- 控制台也会显示实时日志

## 联系支持

如有问题，请查看：
- 项目文档：`README.md`
- 开发状态：`PROJECT_STATUS.md`
- 系统日志：`logs/vpp_ai.log`

---

**最后更新**: 2025年6月15日
