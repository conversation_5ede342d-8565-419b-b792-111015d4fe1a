<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VPP-AI 新能源微网决策系统 - 原型图导航</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .title {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 18px;
            margin-bottom: 30px;
        }
        
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 40px;
            padding: 20px;
        }
        
        .nav-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
            border-color: #3498db;
        }
        
        .nav-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 20px;
            display: flex;
            align-items: center;
        }
        
        .nav-card .icon {
            font-size: 24px;
            margin-right: 10px;
        }
        
        .nav-card p {
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .page-count {
            background: #3498db;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            display: inline-block;
        }
        
        .system-logic {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            text-align: center;
            padding: 30px;
            margin: 20px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .system-logic:hover {
            transform: scale(1.02);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 40px 20px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: #3498db;
        }
        
        .stat-label {
            color: #7f8c8d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="title">VPP-AI 新能源微网决策系统</h1>
            <p class="subtitle">Virtual Power Plant with AI - 交互式原型图导航</p>
        </div>
    </div>

    <div class="system-logic" onclick="window.open('system_logic.html', '_blank')">
        <h2>🧠 系统逻辑架构图</h2>
        <p>点击查看完整的系统架构和模块关系图</p>
    </div>

    <div class="stats">
        <div class="stat-card">
            <div class="stat-number">108</div>
            <div class="stat-label">原型界面</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">10</div>
            <div class="stat-label">功能模块</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">1</div>
            <div class="stat-label">系统架构图</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">100%</div>
            <div class="stat-label">交互式体验</div>
        </div>
    </div>

    <div class="nav-grid">
        <div class="nav-card" onclick="window.open('dashboard/main.html', '_blank')">
            <h3><span class="icon">📊</span>主控制台</h3>
            <p>系统总览、实时监控、关键指标展示、快速操作入口</p>
            <span class="page-count">12个页面</span>
        </div>

        <div class="nav-card" onclick="window.open('solar/index.html', '_blank')">
            <h3><span class="icon">🌞</span>光伏发电系统</h3>
            <p>光伏板管理、发电监控、功率预测、效率分析</p>
            <span class="page-count">15个页面</span>
        </div>

        <div class="nav-card" onclick="window.open('storage/index.html', '_blank')">
            <h3><span class="icon">🔋</span>储能管理系统</h3>
            <p>电池监控、充放电控制、SOC管理、寿命分析</p>
            <span class="page-count">18个页面</span>
        </div>

        <div class="nav-card" onclick="window.open('load/index.html', '_blank')">
            <h3><span class="icon">⚡</span>负荷管理系统</h3>
            <p>负荷监控、需求预测、负荷调度、用电分析</p>
            <span class="page-count">16个页面</span>
        </div>

        <div class="nav-card" onclick="window.open('grid/index.html', '_blank')">
            <h3><span class="icon">🔌</span>电网交易系统</h3>
            <p>电价监控、交易管理、售电策略、收益分析</p>
            <span class="page-count">14个页面</span>
        </div>

        <div class="nav-card" onclick="window.open('weather/index.html', '_blank')">
            <h3><span class="icon">🌤️</span>天气数据系统</h3>
            <p>天气监控、气象预报、辐照度分析、环境监测</p>
            <span class="page-count">10个页面</span>
        </div>

        <div class="nav-card" onclick="window.open('optimization/index.html', '_blank')">
            <h3><span class="icon">🧠</span>智能优化引擎</h3>
            <p>优化策略、算法配置、决策分析、性能评估</p>
            <span class="page-count">12个页面</span>
        </div>

        <div class="nav-card" onclick="window.open('digital_twin/index.html', '_blank')">
            <h3><span class="icon">🏭</span>数字孪生系统</h3>
            <p>3D建模、实时仿真、场景分析、设备孪生</p>
            <span class="page-count">8个页面</span>
        </div>

        <div class="nav-card" onclick="window.open('system/index.html', '_blank')">
            <h3><span class="icon">⚙️</span>系统管理</h3>
            <p>用户管理、权限控制、系统配置、日志管理</p>
            <span class="page-count">10个页面</span>
        </div>

        <div class="nav-card" onclick="window.open('reports/index.html', '_blank')">
            <h3><span class="icon">📈</span>报表分析</h3>
            <p>数据报表、趋势分析、财务报告、运营统计</p>
            <span class="page-count">13个页面</span>
        </div>
    </div>

    <div style="text-align: center; padding: 40px; color: rgba(255,255,255,0.8);">
        <p>© 2025 VPP-AI 新能源微网决策系统 - 原型设计</p>
        <p>点击任意模块卡片进入对应的功能原型界面</p>
    </div>
</body>
</html>
