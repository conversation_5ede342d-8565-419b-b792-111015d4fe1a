<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端 - VPP-AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea22 0%, #667eea44 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .title {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb a {
            color: #3498db;
            text-decoration: none;
            margin: 0 5px;
        }
        
        .content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            min-height: 500px;
        }
        
        .back-btn {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 20px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #667eea;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .feature-desc {
            color: #7f8c8d;
            line-height: 1.6;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #27ae60;
            margin-right: 8px;
        }
        
        .mobile-container {
            max-width: 375px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .mobile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .mobile-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            padding: 20px;
        }

        .mobile-stat {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .mobile-value {
            font-size: 20px;
            font-weight: bold;
            color: #667eea;
        }

        .mobile-label {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
        }

        .mobile-chart {
            height: 150px;
            background: #f8f9fa;
            margin: 0 20px 20px 20px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
        }

        .mobile-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            padding: 0 20px 20px 20px;
        }

        .mobile-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
        }

        .placeholder {
            text-align: center;
            color: #7f8c8d;
            font-size: 18px;
            margin: 50px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="title">
                <span>📊</span>
                <span>移动端</span>
            </h1>
            <p class="subtitle">移动设备界面</p>
            <div class="nav-breadcrumb">
                <a href="../index.html">🏠 首页</a> > 
                <a href="../dashboard/main.html">📊 控制台</a> > 
                <a href="index.html">📊 主控制台</a> > 
                <span>移动端</span>
            </div>
        </div>
    </div>

    <a href="index.html" class="back-btn">← 返回主控制台</a>

    <div class="content">
        <h2 style="color: #2c3e50; margin-bottom: 20px;">
            <span class="status-indicator"></span>移动端
        </h2>
        
        <div class="mobile-container">
            <div class="mobile-header">
                <h3>🏭 VPP-AI</h3>
                <p style="font-size: 14px; opacity: 0.9;">新能源微网控制中心</p>
            </div>

            <div class="mobile-stats">
                <div class="mobile-stat">
                    <div class="mobile-value">156.8 kW</div>
                    <div class="mobile-label">当前功率</div>
                </div>
                <div class="mobile-stat">
                    <div class="mobile-value">18.5%</div>
                    <div class="mobile-label">电池SOC</div>
                </div>
                <div class="mobile-stat">
                    <div class="mobile-value">¥2,456</div>
                    <div class="mobile-label">今日收益</div>
                </div>
                <div class="mobile-stat">
                    <div class="mobile-value">正常</div>
                    <div class="mobile-label">系统状态</div>
                </div>
            </div>

            <div class="mobile-chart">
                📈 实时功率曲线
            </div>

            <div class="mobile-actions">
                <button class="mobile-btn">🌞 光伏监控</button>
                <button class="mobile-btn">🔋 储能管理</button>
                <button class="mobile-btn">⚡ 负荷控制</button>
                <button class="mobile-btn">🚨 告警中心</button>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px; color: #7f8c8d;">
            <p>📱 移动端适配界面演示</p>
            <p><small>支持手机、平板等移动设备访问</small></p>
        </div>
    </div>

    <div style="text-align: center; padding: 40px; color: rgba(0,0,0,0.6);">
        <p>© 2025 VPP-AI 主控制台 - 移动端</p>
        <p>原型界面展示，实际功能正在开发中</p>
    </div>
</body>
</html>