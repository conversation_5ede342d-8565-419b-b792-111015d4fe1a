<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VPP-AI 主控制台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        
        .nav-menu {
            display: flex;
            gap: 20px;
        }
        
        .nav-item {
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: white;
        }
        
        .nav-item:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .main-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            min-height: calc(100vh - 70px);
        }
        
        .sidebar {
            background: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 20px 0;
        }
        
        .sidebar-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .sidebar-item:hover {
            background: #f8f9fa;
            border-left-color: #667eea;
        }
        
        .sidebar-item.active {
            background: #e3f2fd;
            border-left-color: #2196f3;
            color: #2196f3;
        }
        
        .content {
            padding: 20px;
            overflow-y: auto;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .card-icon {
            font-size: 24px;
        }
        
        .metric-value {
            font-size: 32px;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }
        
        .metric-label {
            color: #7f8c8d;
            font-size: 14px;
        }
        
        .metric-change {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            margin-top: 5px;
            display: inline-block;
        }
        
        .metric-change.positive {
            background: #d4edda;
            color: #155724;
        }
        
        .metric-change.negative {
            background: #f8d7da;
            color: #721c24;
        }
        
        .chart-container {
            height: 200px;
            background: #f8f9fa;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            margin-top: 15px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-online { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-offline { background: #dc3545; }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .alert-panel {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .alert-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 10px;
        }
        
        .alert-item {
            padding: 8px 0;
            border-bottom: 1px solid #ffeaa7;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .alert-item:last-child {
            border-bottom: none;
        }
        
        .time-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .time-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .time-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🏭 VPP-AI 控制中心</div>
        <div class="nav-menu">
            <a href="../index.html" class="nav-item">🏠 首页</a>
            <a href="overview.html" class="nav-item">📊 总览</a>
            <a href="realtime.html" class="nav-item">⚡ 实时</a>
            <a href="alerts.html" class="nav-item">🔔 告警</a>
        </div>
        <div class="user-info">
            <span>👤 管理员</span>
            <span>|</span>
            <span>🕐 2025-06-15 16:30</span>
        </div>
    </div>

    <div class="main-container">
        <div class="sidebar">
            <div class="sidebar-item active">
                <span>📊</span> 主控制台
            </div>
            <div class="sidebar-item" onclick="window.open('../solar/index.html', '_blank')">
                <span>🌞</span> 光伏系统
            </div>
            <div class="sidebar-item" onclick="window.open('../storage/index.html', '_blank')">
                <span>🔋</span> 储能系统
            </div>
            <div class="sidebar-item" onclick="window.open('../load/index.html', '_blank')">
                <span>⚡</span> 负荷管理
            </div>
            <div class="sidebar-item" onclick="window.open('../grid/index.html', '_blank')">
                <span>🔌</span> 电网交易
            </div>
            <div class="sidebar-item" onclick="window.open('../weather/index.html', '_blank')">
                <span>🌤️</span> 天气监控
            </div>
            <div class="sidebar-item" onclick="window.open('../optimization/index.html', '_blank')">
                <span>🧠</span> 智能优化
            </div>
            <div class="sidebar-item" onclick="window.open('../digital_twin/index.html', '_blank')">
                <span>🏭</span> 数字孪生
            </div>
            <div class="sidebar-item" onclick="window.open('../reports/index.html', '_blank')">
                <span>📈</span> 报表分析
            </div>
            <div class="sidebar-item" onclick="window.open('../system/index.html', '_blank')">
                <span>⚙️</span> 系统管理
            </div>
        </div>

        <div class="content">
            <div class="time-selector">
                <button class="time-btn active">实时</button>
                <button class="time-btn">今日</button>
                <button class="time-btn">本周</button>
                <button class="time-btn">本月</button>
                <button class="time-btn">自定义</button>
            </div>

            <div class="alert-panel">
                <div class="alert-title">🔔 系统告警 (3条)</div>
                <div class="alert-item">
                    <span>⚠️ 储能电池SOC低于20%</span>
                    <span>16:25</span>
                </div>
                <div class="alert-item">
                    <span>📊 光伏发电功率异常波动</span>
                    <span>16:20</span>
                </div>
                <div class="alert-item">
                    <span>💰 电价即将进入峰时段</span>
                    <span>16:15</span>
                </div>
            </div>

            <div class="dashboard-grid">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">系统状态</div>
                        <div class="card-icon">🟢</div>
                    </div>
                    <div>
                        <div class="metric-value">正常运行</div>
                        <div class="metric-label">
                            <span class="status-indicator status-online"></span>光伏系统
                        </div>
                        <div class="metric-label">
                            <span class="status-indicator status-warning"></span>储能系统
                        </div>
                        <div class="metric-label">
                            <span class="status-indicator status-online"></span>负荷系统
                        </div>
                        <div class="metric-label">
                            <span class="status-indicator status-online"></span>电网连接
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">实时功率</div>
                        <div class="card-icon">⚡</div>
                    </div>
                    <div>
                        <div class="metric-value">156.8 kW</div>
                        <div class="metric-label">当前总功率</div>
                        <div class="metric-change positive">↗ +12.5% 较昨日</div>
                    </div>
                    <div class="chart-container">
                        📈 实时功率曲线图
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">今日发电量</div>
                        <div class="card-icon">🌞</div>
                    </div>
                    <div>
                        <div class="metric-value">1,248 kWh</div>
                        <div class="metric-label">累计发电量</div>
                        <div class="metric-change positive">↗ +8.3% 较昨日</div>
                    </div>
                    <div class="chart-container">
                        📊 发电量趋势图
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">储能状态</div>
                        <div class="card-icon">🔋</div>
                    </div>
                    <div>
                        <div class="metric-value">18.5%</div>
                        <div class="metric-label">电池SOC</div>
                        <div class="metric-change negative">↘ 充电中</div>
                    </div>
                    <div class="chart-container">
                        🔋 SOC变化曲线
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">今日收益</div>
                        <div class="card-icon">💰</div>
                    </div>
                    <div>
                        <div class="metric-value">¥2,456</div>
                        <div class="metric-label">净收益</div>
                        <div class="metric-change positive">↗ +15.2% 较昨日</div>
                    </div>
                    <div class="chart-container">
                        💹 收益分析图
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">负荷情况</div>
                        <div class="card-icon">📊</div>
                    </div>
                    <div>
                        <div class="metric-value">89.2 kW</div>
                        <div class="metric-label">当前负荷</div>
                        <div class="metric-change positive">↗ 负荷率 67%</div>
                    </div>
                    <div class="chart-container">
                        📈 负荷预测曲线
                    </div>
                </div>
            </div>

            <div class="quick-actions">
                <button class="action-btn" onclick="window.open('emergency.html', '_blank')">
                    🚨 应急控制
                </button>
                <button class="action-btn" onclick="window.open('optimization_run.html', '_blank')">
                    🧠 运行优化
                </button>
                <button class="action-btn" onclick="window.open('manual_control.html', '_blank')">
                    🎮 手动控制
                </button>
                <button class="action-btn" onclick="window.open('export_data.html', '_blank')">
                    📤 导出数据
                </button>
                <button class="action-btn" onclick="window.open('system_settings.html', '_blank')">
                    ⚙️ 系统设置
                </button>
                <button class="action-btn" onclick="window.open('help.html', '_blank')">
                    ❓ 帮助支持
                </button>
            </div>
        </div>
    </div>
</body>
</html>
