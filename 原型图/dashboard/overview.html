<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统总览 - VPP-AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea22 0%, #667eea44 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .title {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb a {
            color: #3498db;
            text-decoration: none;
            margin: 0 5px;
        }
        
        .content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            min-height: 500px;
        }
        
        .back-btn {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 20px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #667eea;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .feature-desc {
            color: #7f8c8d;
            line-height: 1.6;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #27ae60;
            margin-right: 8px;
        }
        
        .placeholder {
            text-align: center;
            color: #7f8c8d;
            font-size: 18px;
            margin: 50px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="title">
                <span>📊</span>
                <span>系统总览</span>
            </h1>
            <p class="subtitle">整体运行状态概览</p>
            <div class="nav-breadcrumb">
                <a href="../index.html">🏠 首页</a> > 
                <a href="../dashboard/main.html">📊 控制台</a> > 
                <a href="index.html">📊 主控制台</a> > 
                <span>系统总览</span>
            </div>
        </div>
    </div>

    <a href="index.html" class="back-btn">← 返回主控制台</a>

    <div class="content">
        <h2 style="color: #2c3e50; margin-bottom: 20px;">
            <span class="status-indicator"></span>系统总览
        </h2>
        
        <!-- 系统总览仪表板 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
            <div class="feature-card" style="text-align: center; border-left: 4px solid #667eea;">
                <div style="font-size: 32px; font-weight: bold; color: #667eea; margin-bottom: 10px;">98.5%</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">系统可用率</div>
                <div style="font-size: 12px; color: #27ae60;">✅ 运行稳定</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #f39c12;">
                <div style="font-size: 32px; font-weight: bold; color: #f39c12; margin-bottom: 10px;">1,248 kWh</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">今日发电量</div>
                <div style="font-size: 12px; color: #27ae60;">📈 +8.3% 较昨日</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #74b9ff;">
                <div style="font-size: 32px; font-weight: bold; color: #74b9ff; margin-bottom: 10px;">18.5%</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">储能SOC</div>
                <div style="font-size: 12px; color: #e74c3c;">⚠️ 低电量告警</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #00b894;">
                <div style="font-size: 32px; font-weight: bold; color: #00b894; margin-bottom: 10px;">89.2 kW</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">当前负荷</div>
                <div style="font-size: 12px; color: #00b894;">📊 负荷率 67%</div>
            </div>
        </div>
        
        <!-- 系统架构图 -->
        <div class="feature-card" style="margin-bottom: 30px;">
            <div class="feature-title">🏗️ 系统架构总览</div>
            <div style="height: 400px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #6c757d; margin-top: 15px; position: relative;">
                <div style="position: absolute; top: 50px; left: 100px; width: 120px; height: 80px; background: #ffeaa7; border: 2px solid #fdcb6e; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #2c3e50;">🌞<br>光伏系统<br>156.8kW</div>
                <div style="position: absolute; top: 50px; right: 100px; width: 120px; height: 80px; background: #a8e6cf; border: 2px solid #00b894; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #2c3e50;">⚡<br>负荷系统<br>89.2kW</div>
                <div style="position: absolute; bottom: 50px; left: 100px; width: 120px; height: 80px; background: #dde5ff; border: 2px solid #74b9ff; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #2c3e50;">🔋<br>储能系统<br>18.5%SOC</div>
                <div style="position: absolute; bottom: 50px; right: 100px; width: 120px; height: 80px; background: #e8d5ff; border: 2px solid #a29bfe; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #2c3e50;">🔌<br>电网交易<br>¥2,456</div>
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 100px; height: 100px; background: #ff9ff3; border: 3px solid #fd79a8; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #2c3e50; text-align: center;">🧠<br>智能<br>控制中心</div>
                <!-- 连接线 -->
                <svg style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none;">
                    <line x1="160" y1="90" x2="300" y2="200" stroke="#667eea" stroke-width="3" stroke-dasharray="5,5"/>
                    <line x1="440" y1="90" x2="400" y2="200" stroke="#667eea" stroke-width="3" stroke-dasharray="5,5"/>
                    <line x1="160" y1="290" x2="300" y2="250" stroke="#667eea" stroke-width="3" stroke-dasharray="5,5"/>
                    <line x1="440" y1="290" x2="400" y2="250" stroke="#667eea" stroke-width="3" stroke-dasharray="5,5"/>
                </svg>
            </div>
        </div>
                <div class="feature-desc">实时数据图表和统计信息展示</div>
            </div>
            <div class="feature-card">
                <div class="feature-title">⚙️ 功能控制</div>
                <div class="feature-desc">相关功能的操作控制界面</div>
            </div>
            <div class="feature-card">
                <div class="feature-title">📈 趋势分析</div>
                <div class="feature-desc">历史数据趋势分析和预测</div>
            </div>
            <div class="feature-card">
                <div class="feature-title">🔔 告警提醒</div>
                <div class="feature-desc">相关告警信息和处理建议</div>
            </div>
        </div>
    </div>

    <div style="text-align: center; padding: 40px; color: rgba(0,0,0,0.6);">
        <p>© 2025 VPP-AI 主控制台 - 系统总览</p>
        <p>原型界面展示，实际功能正在开发中</p>
    </div>
</body>
</html>