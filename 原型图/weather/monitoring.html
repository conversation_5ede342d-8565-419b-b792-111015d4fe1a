<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时监控 - VPP-AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #00cec922 0%, #00cec944 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .title {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb a {
            color: #3498db;
            text-decoration: none;
            margin: 0 5px;
        }
        
        .content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            min-height: 500px;
        }
        
        .back-btn {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 20px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #00cec9;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .feature-desc {
            color: #7f8c8d;
            line-height: 1.6;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #27ae60;
            margin-right: 8px;
        }
        
        .placeholder {
            text-align: center;
            color: #7f8c8d;
            font-size: 18px;
            margin: 50px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="title">
                <span>🌤️</span>
                <span>实时监控</span>
            </h1>
            <p class="subtitle">天气实时监控</p>
            <div class="nav-breadcrumb">
                <a href="../index.html">🏠 首页</a> > 
                <a href="../dashboard/main.html">📊 控制台</a> > 
                <a href="index.html">🌤️ 天气数据系统</a> > 
                <span>实时监控</span>
            </div>
        </div>
    </div>

    <a href="index.html" class="back-btn">← 返回天气数据系统</a>

    <div class="content">
        <h2 style="color: #2c3e50; margin-bottom: 20px;">
            <span class="status-indicator"></span>实时监控
        </h2>
        
        <!-- 天气概览 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">
            <div class="feature-card" style="text-align: center; border-left: 4px solid #00cec9;">
                <div style="font-size: 28px; font-weight: bold; color: #00cec9; margin-bottom: 5px;">28.5°C</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">环境温度</div>
                <div style="font-size: 12px; color: #00b894;">🌡️ 适宜温度</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #fdcb6e;">
                <div style="font-size: 28px; font-weight: bold; color: #fdcb6e; margin-bottom: 5px;">850 W/m²</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">太阳辐照度</div>
                <div style="font-size: 12px; color: #00b894;">☀️ 辐照强烈</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #74b9ff;">
                <div style="font-size: 28px; font-weight: bold; color: #74b9ff; margin-bottom: 5px;">3.2 m/s</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">风速</div>
                <div style="font-size: 12px; color: #00b894;">🌬️ 微风</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #a29bfe;">
                <div style="font-size: 28px; font-weight: bold; color: #a29bfe; margin-bottom: 5px;">65%</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">相对湿度</div>
                <div style="font-size: 12px; color: #00b894;">💧 湿度适中</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #fd79a8;">
                <div style="font-size: 28px; font-weight: bold; color: #fd79a8; margin-bottom: 5px;">30%</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">云量覆盖</div>
                <div style="font-size: 12px; color: #00b894;">☁️ 少云</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #6c5ce7;">
                <div style="font-size: 28px; font-weight: bold; color: #6c5ce7; margin-bottom: 5px;">1013 hPa</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">大气压强</div>
                <div style="font-size: 12px; color: #00b894;">📊 标准气压</div>
            </div>
        </div>

        <!-- 天气详情 -->
        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin-bottom: 30px;">
            <div class="feature-card">
                <div class="feature-title">🌤️ 24小时天气变化趋势</div>
                <div style="height: 300px; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #6c757d; margin-top: 15px;">
                    📊 天气参数变化曲线图
                    <br>显示温度、湿度、辐照度、风速等参数的24小时变化趋势
                </div>
                <div style="display: flex; justify-content: space-between; margin-top: 10px; font-size: 12px; color: #7f8c8d;">
                    <span>🔴 温度</span>
                    <span>🔵 湿度</span>
                    <span>🟡 辐照度</span>
                    <span>🟢 风速</span>
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-title">🔮 天气预报</div>
                <div style="margin-top: 15px;">
                    <div style="background: #e8f5e8; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #27ae60; margin-bottom: 10px;">☀️ 今日: 晴转多云</div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>最高温度:</span>
                            <span style="font-weight: bold;">32°C</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>最低温度:</span>
                            <span style="font-weight: bold;">22°C</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>降水概率:</span>
                            <span style="font-weight: bold;">10%</span>
                        </div>
                    </div>
                    
                    <div style="background: #fff3cd; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #856404; margin-bottom: 10px;">🌦️ 明日: 多云转阴</div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>最高温度:</span>
                            <span style="font-weight: bold;">28°C</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>最低温度:</span>
                            <span style="font-weight: bold;">20°C</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>降水概率:</span>
                            <span style="font-weight: bold;">40%</span>
                        </div>
                    </div>
                    
                    <div style="background: #f8d7da; border-radius: 8px; padding: 15px;">
                        <div style="font-weight: bold; color: #721c24; margin-bottom: 10px;">🌧️ 后天: 小雨</div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>最高温度:</span>
                            <span style="font-weight: bold;">25°C</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>最低温度:</span>
                            <span style="font-weight: bold;">18°C</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>降水概率:</span>
                            <span style="font-weight: bold;">80%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                <div class="feature-desc">实时数据图表和统计信息展示</div>
            </div>
            <div class="feature-card">
                <div class="feature-title">⚙️ 功能控制</div>
                <div class="feature-desc">相关功能的操作控制界面</div>
            </div>
            <div class="feature-card">
                <div class="feature-title">📈 趋势分析</div>
                <div class="feature-desc">历史数据趋势分析和预测</div>
            </div>
            <div class="feature-card">
                <div class="feature-title">🔔 告警提醒</div>
                <div class="feature-desc">相关告警信息和处理建议</div>
            </div>
        </div>
    </div>

    <div style="text-align: center; padding: 40px; color: rgba(0,0,0,0.6);">
        <p>© 2025 VPP-AI 天气数据系统 - 实时监控</p>
        <p>原型界面展示，实际功能正在开发中</p>
    </div>
</body>
</html>