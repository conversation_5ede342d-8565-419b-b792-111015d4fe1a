<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>光伏发电系统 - VPP-AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .title {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb a {
            color: #3498db;
            text-decoration: none;
            margin: 0 5px;
        }
        
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 30px;
            padding: 20px;
        }
        
        .nav-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }
        
        .nav-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #f39c12, #e67e22);
        }
        
        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
            border-color: #f39c12;
        }
        
        .nav-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
        }
        
        .nav-card .icon {
            font-size: 24px;
            margin-right: 10px;
        }
        
        .nav-card p {
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 15px;
            font-size: 14px;
        }
        
        .status-badge {
            background: #27ae60;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            display: inline-block;
        }
        
        .status-badge.warning {
            background: #f39c12;
        }
        
        .status-badge.error {
            background: #e74c3c;
        }
        
        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 28px;
            font-weight: bold;
            color: #f39c12;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-size: 14px;
        }
        
        .back-btn {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 20px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="title">
                <span>🌞</span>
                <span>光伏发电系统</span>
            </h1>
            <p class="subtitle">Solar Power Generation System</p>
            <div class="nav-breadcrumb">
                <a href="../index.html">🏠 首页</a> > 
                <a href="../dashboard/main.html">📊 控制台</a> > 
                <span>🌞 光伏系统</span>
            </div>
        </div>
    </div>

    <a href="../dashboard/main.html" class="back-btn">← 返回控制台</a>

    <div class="quick-stats">
        <div class="stat-card">
            <div class="stat-number">156.8 kW</div>
            <div class="stat-label">当前发电功率</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">1,248 kWh</div>
            <div class="stat-label">今日发电量</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">22.1%</div>
            <div class="stat-label">转换效率</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">98.5%</div>
            <div class="stat-label">系统可用率</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">850 W/m²</div>
            <div class="stat-label">当前辐照度</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">45.2°C</div>
            <div class="stat-label">组件温度</div>
        </div>
    </div>

    <div class="nav-grid">
        <div class="nav-card" onclick="window.open('monitoring.html', '_blank')">
            <h3><span class="icon">📊</span>实时监控</h3>
            <p>光伏发电实时数据监控，包括功率、电压、电流、温度等关键参数的实时显示和历史趋势分析</p>
            <span class="status-badge">运行中</span>
        </div>

        <div class="nav-card" onclick="window.open('panels.html', '_blank')">
            <h3><span class="icon">🔧</span>光伏板管理</h3>
            <p>光伏板设备信息管理，包括设备档案、安装参数、维护记录、性能评估等功能</p>
            <span class="status-badge">正常</span>
        </div>

        <div class="nav-card" onclick="window.open('prediction.html', '_blank')">
            <h3><span class="icon">🔮</span>发电预测</h3>
            <p>基于天气预报和历史数据的AI发电功率预测，支持短期、中期和长期预测分析</p>
            <span class="status-badge">运行中</span>
        </div>

        <div class="nav-card" onclick="window.open('efficiency.html', '_blank')">
            <h3><span class="icon">📈</span>效率分析</h3>
            <p>光伏系统发电效率分析，包括理论值对比、损失分析、优化建议等</p>
            <span class="status-badge">正常</span>
        </div>

        <div class="nav-card" onclick="window.open('inverter.html', '_blank')">
            <h3><span class="icon">🔄</span>逆变器管理</h3>
            <p>逆变器设备监控和管理，包括运行状态、效率监控、故障诊断等功能</p>
            <span class="status-badge">正常</span>
        </div>

        <div class="nav-card" onclick="window.open('maintenance.html', '_blank')">
            <h3><span class="icon">🔧</span>维护管理</h3>
            <p>设备维护计划制定、维护记录管理、备件库存管理、维护成本分析</p>
            <span class="status-badge">正常</span>
        </div>

        <div class="nav-card" onclick="window.open('weather_impact.html', '_blank')">
            <h3><span class="icon">🌤️</span>天气影响分析</h3>
            <p>天气条件对光伏发电的影响分析，包括云量、温度、湿度等因素的影响评估</p>
            <span class="status-badge">运行中</span>
        </div>

        <div class="nav-card" onclick="window.open('performance.html', '_blank')">
            <h3><span class="icon">🎯</span>性能评估</h3>
            <p>光伏系统整体性能评估，包括发电量统计、性能比分析、同比环比分析</p>
            <span class="status-badge">正常</span>
        </div>

        <div class="nav-card" onclick="window.open('alerts.html', '_blank')">
            <h3><span class="icon">🚨</span>告警管理</h3>
            <p>光伏系统告警信息管理，包括实时告警、历史告警、告警规则配置</p>
            <span class="status-badge warning">3条告警</span>
        </div>

        <div class="nav-card" onclick="window.open('reports.html', '_blank')">
            <h3><span class="icon">📋</span>报表中心</h3>
            <p>光伏系统各类报表生成，包括日报、月报、年报、自定义报表等</p>
            <span class="status-badge">正常</span>
        </div>

        <div class="nav-card" onclick="window.open('optimization.html', '_blank')">
            <h3><span class="icon">⚡</span>发电优化</h3>
            <p>光伏发电优化策略，包括最大功率点跟踪、阴影优化、清洁建议等</p>
            <span class="status-badge">运行中</span>
        </div>

        <div class="nav-card" onclick="window.open('comparison.html', '_blank')">
            <h3><span class="icon">📊</span>对比分析</h3>
            <p>不同时期、不同条件下的发电数据对比分析，支持多维度数据对比</p>
            <span class="status-badge">正常</span>
        </div>

        <div class="nav-card" onclick="window.open('export.html', '_blank')">
            <h3><span class="icon">📤</span>数据导出</h3>
            <p>光伏系统数据导出功能，支持多种格式导出，包括Excel、CSV、PDF等</p>
            <span class="status-badge">正常</span>
        </div>

        <div class="nav-card" onclick="window.open('settings.html', '_blank')">
            <h3><span class="icon">⚙️</span>系统设置</h3>
            <p>光伏系统参数配置，包括设备参数、告警阈值、数据采集间隔等设置</p>
            <span class="status-badge">正常</span>
        </div>

        <div class="nav-card" onclick="window.open('mobile.html', '_blank')">
            <h3><span class="icon">📱</span>移动端监控</h3>
            <p>移动设备适配的光伏系统监控界面，支持手机、平板等移动设备访问</p>
            <span class="status-badge">正常</span>
        </div>
    </div>

    <div style="text-align: center; padding: 40px; color: rgba(0,0,0,0.6);">
        <p>© 2025 VPP-AI 光伏发电系统管理平台</p>
        <p>点击任意功能卡片进入对应的详细界面</p>
    </div>
</body>
</html>
