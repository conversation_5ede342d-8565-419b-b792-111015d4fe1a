<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发电预测 - VPP-AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #f39c1222 0%, #f39c1244 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .title {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb a {
            color: #3498db;
            text-decoration: none;
            margin: 0 5px;
        }
        
        .content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            min-height: 500px;
        }
        
        .back-btn {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 20px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #f39c12;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .feature-desc {
            color: #7f8c8d;
            line-height: 1.6;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #27ae60;
            margin-right: 8px;
        }
        
        .placeholder {
            text-align: center;
            color: #7f8c8d;
            font-size: 18px;
            margin: 50px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="title">
                <span>🌞</span>
                <span>发电预测</span>
            </h1>
            <p class="subtitle">AI发电功率预测</p>
            <div class="nav-breadcrumb">
                <a href="../index.html">🏠 首页</a> > 
                <a href="../dashboard/main.html">📊 控制台</a> > 
                <a href="index.html">🌞 光伏发电系统</a> > 
                <span>发电预测</span>
            </div>
        </div>
    </div>

    <a href="index.html" class="back-btn">← 返回光伏发电系统</a>

    <div class="content">
        <h2 style="color: #2c3e50; margin-bottom: 20px;">
            <span class="status-indicator"></span>发电预测
        </h2>
        
        <!-- 预测概览卡片 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">
            <div class="feature-card" style="text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #f39c12; margin-bottom: 5px;">1,456 kWh</div>
                <div style="color: #7f8c8d;">明日预测发电量</div>
                <div style="font-size: 12px; color: #27ae60; margin-top: 5px;">↗ +8.5% 较今日</div>
            </div>
            <div class="feature-card" style="text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #f39c12; margin-bottom: 5px;">185.2 kW</div>
                <div style="color: #7f8c8d;">峰值功率预测</div>
                <div style="font-size: 12px; color: #27ae60; margin-top: 5px;">预计 13:30</div>
            </div>
            <div class="feature-card" style="text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #f39c12; margin-bottom: 5px;">92.3%</div>
                <div style="color: #7f8c8d;">预测准确率</div>
                <div style="font-size: 12px; color: #27ae60; margin-top: 5px;">过去7天平均</div>
            </div>
            <div class="feature-card" style="text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #f39c12; margin-bottom: 5px;">晴转多云</div>
                <div style="color: #7f8c8d;">明日天气</div>
                <div style="font-size: 12px; color: #e67e22; margin-top: 5px;">辐照度 750 W/m²</div>
            </div>
        </div>

        <!-- 预测图表区域 -->
        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin-bottom: 30px;">
            <div class="feature-card">
                <div class="feature-title">📈 24小时发电功率预测曲线</div>
                <div style="height: 300px; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #6c757d; margin-top: 15px; position: relative;">
                    <div style="position: absolute; top: 10px; right: 10px; font-size: 12px; color: #7f8c8d;">
                        🤖 AI预测模型: LSTM + 天气数据
                    </div>
                    📊 功率预测曲线图
                    <br>显示未来24小时的发电功率预测
                    <br>包含置信区间和历史对比
                </div>
                <div style="display: flex; justify-content: space-between; margin-top: 10px; font-size: 12px; color: #7f8c8d;">
                    <span>🔵 预测值</span>
                    <span>🟡 置信区间</span>
                    <span>🔴 历史同期</span>
                    <span>🟢 实际值</span>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-title">🌤️ 天气影响因子</div>
                <div style="margin-top: 15px;">
                    <div style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span style="font-size: 14px;">太阳辐照度</span>
                            <span style="font-weight: bold; color: #f39c12;">750 W/m²</span>
                        </div>
                        <div style="background: #eee; height: 8px; border-radius: 4px;">
                            <div style="background: #f39c12; height: 8px; width: 75%; border-radius: 4px;"></div>
                        </div>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span style="font-size: 14px;">云量覆盖</span>
                            <span style="font-weight: bold; color: #74b9ff;">35%</span>
                        </div>
                        <div style="background: #eee; height: 8px; border-radius: 4px;">
                            <div style="background: #74b9ff; height: 8px; width: 35%; border-radius: 4px;"></div>
                        </div>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span style="font-size: 14px;">环境温度</span>
                            <span style="font-weight: bold; color: #e17055;">28°C</span>
                        </div>
                        <div style="background: #eee; height: 8px; border-radius: 4px;">
                            <div style="background: #e17055; height: 8px; width: 70%; border-radius: 4px;"></div>
                        </div>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span style="font-size: 14px;">湿度</span>
                            <span style="font-weight: bold; color: #00b894;">65%</span>
                        </div>
                        <div style="background: #eee; height: 8px; border-radius: 4px;">
                            <div style="background: #00b894; height: 8px; width: 65%; border-radius: 4px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
            <div class="feature-card">
                <div class="feature-title">🔔 告警提醒</div>
                <div class="feature-desc">相关告警信息和处理建议</div>
            </div>
        </div>
    </div>

    <div style="text-align: center; padding: 40px; color: rgba(0,0,0,0.6);">
        <p>© 2025 VPP-AI 光伏发电系统 - 发电预测</p>
        <p>原型界面展示，实际功能正在开发中</p>
    </div>
</body>
</html>