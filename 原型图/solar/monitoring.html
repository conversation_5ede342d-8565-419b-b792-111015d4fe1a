<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>光伏实时监控 - VPP-AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .logo {
            font-size: 20px;
            font-weight: bold;
        }
        
        .nav-menu {
            display: flex;
            gap: 20px;
        }
        
        .nav-item {
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: white;
        }
        
        .nav-item:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .main-container {
            padding: 20px;
        }
        
        .breadcrumb {
            margin-bottom: 20px;
            color: #7f8c8d;
        }
        
        .breadcrumb a {
            color: #3498db;
            text-decoration: none;
        }
        
        .monitoring-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .main-chart {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .chart-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .chart-controls {
            display: flex;
            gap: 10px;
        }
        
        .control-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .control-btn.active {
            background: #f39c12;
            color: white;
            border-color: #f39c12;
        }
        
        .chart-area {
            height: 300px;
            background: #f8f9fa;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            position: relative;
        }
        
        .realtime-data {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .data-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }
        
        .data-item:last-child {
            border-bottom: none;
        }
        
        .data-label {
            color: #7f8c8d;
            font-size: 14px;
        }
        
        .data-value {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .data-value.good {
            color: #27ae60;
        }
        
        .data-value.warning {
            color: #f39c12;
        }
        
        .data-value.danger {
            color: #e74c3c;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-online { background: #27ae60; }
        .status-warning { background: #f39c12; }
        .status-offline { background: #e74c3c; }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .metric-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .metric-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #f39c12;
            margin-bottom: 5px;
        }
        
        .metric-label {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .metric-trend {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 10px;
            display: inline-block;
        }
        
        .metric-trend.up {
            background: #d4edda;
            color: #155724;
        }
        
        .metric-trend.down {
            background: #f8d7da;
            color: #721c24;
        }
        
        .metric-trend.stable {
            background: #e2e3e5;
            color: #383d41;
        }
        
        .panel-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .panel-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .panel-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .panel-id {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .panel-status {
            width: 10px;
            height: 10px;
            border-radius: 50%;
        }
        
        .panel-power {
            font-size: 18px;
            font-weight: bold;
            color: #f39c12;
            margin-bottom: 5px;
        }
        
        .panel-efficiency {
            font-size: 12px;
            color: #7f8c8d;
        }
        
        .alert-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .alert-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .alert-title {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .alert-count {
            background: #e74c3c;
            color: white;
            padding: 4px 8px;
            border-radius: 10px;
            font-size: 12px;
        }
        
        .alert-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .alert-item:last-child {
            border-bottom: none;
        }
        
        .alert-icon {
            margin-right: 10px;
            font-size: 16px;
        }
        
        .alert-message {
            flex: 1;
            color: #2c3e50;
        }
        
        .alert-time {
            color: #7f8c8d;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🌞 光伏实时监控</div>
        <div class="nav-menu">
            <a href="index.html" class="nav-item">← 返回光伏系统</a>
            <a href="../dashboard/main.html" class="nav-item">🏠 主控制台</a>
            <a href="prediction.html" class="nav-item">🔮 发电预测</a>
            <a href="alerts.html" class="nav-item">🚨 告警管理</a>
        </div>
        <div style="color: white;">
            🕐 2025-06-15 16:35:42 | 🔄 自动刷新
        </div>
    </div>

    <div class="main-container">
        <div class="breadcrumb">
            <a href="../index.html">首页</a> > 
            <a href="index.html">光伏系统</a> > 
            <span>实时监控</span>
        </div>

        <div class="alert-section">
            <div class="alert-header">
                <div class="alert-title">🚨 实时告警</div>
                <div class="alert-count">2</div>
            </div>
            <div class="alert-item">
                <div class="alert-icon">⚠️</div>
                <div class="alert-message">光伏板#12温度过高 (52.3°C)</div>
                <div class="alert-time">16:32</div>
            </div>
            <div class="alert-item">
                <div class="alert-icon">📉</div>
                <div class="alert-message">逆变器#3效率下降 (91.2%)</div>
                <div class="alert-time">16:28</div>
            </div>
        </div>

        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-icon">⚡</div>
                <div class="metric-value">156.8 kW</div>
                <div class="metric-label">当前发电功率</div>
                <div class="metric-trend up">↗ +5.2%</div>
            </div>
            <div class="metric-card">
                <div class="metric-icon">🌞</div>
                <div class="metric-value">850 W/m²</div>
                <div class="metric-label">太阳辐照度</div>
                <div class="metric-trend stable">→ 稳定</div>
            </div>
            <div class="metric-card">
                <div class="metric-icon">🌡️</div>
                <div class="metric-value">45.2°C</div>
                <div class="metric-label">平均组件温度</div>
                <div class="metric-trend up">↗ +2.1°C</div>
            </div>
            <div class="metric-card">
                <div class="metric-icon">📊</div>
                <div class="metric-value">22.1%</div>
                <div class="metric-label">系统效率</div>
                <div class="metric-trend down">↘ -0.3%</div>
            </div>
        </div>

        <div class="monitoring-grid">
            <div class="main-chart">
                <div class="chart-header">
                    <div class="chart-title">实时功率曲线</div>
                    <div class="chart-controls">
                        <button class="control-btn active">1小时</button>
                        <button class="control-btn">6小时</button>
                        <button class="control-btn">24小时</button>
                        <button class="control-btn">7天</button>
                    </div>
                </div>
                <div class="chart-area">
                    📈 实时功率变化曲线图
                    <br>显示最近1小时的发电功率变化趋势
                </div>
            </div>

            <div class="realtime-data">
                <h3 style="margin-bottom: 15px; color: #2c3e50;">实时数据</h3>
                <div class="data-item">
                    <span class="data-label">
                        <span class="status-indicator status-online"></span>系统状态
                    </span>
                    <span class="data-value good">正常运行</span>
                </div>
                <div class="data-item">
                    <span class="data-label">直流电压</span>
                    <span class="data-value">385.6 V</span>
                </div>
                <div class="data-item">
                    <span class="data-label">直流电流</span>
                    <span class="data-value">407.2 A</span>
                </div>
                <div class="data-item">
                    <span class="data-label">交流电压</span>
                    <span class="data-value">380.1 V</span>
                </div>
                <div class="data-item">
                    <span class="data-label">交流电流</span>
                    <span class="data-value">238.5 A</span>
                </div>
                <div class="data-item">
                    <span class="data-label">功率因数</span>
                    <span class="data-value good">0.98</span>
                </div>
                <div class="data-item">
                    <span class="data-label">频率</span>
                    <span class="data-value">50.02 Hz</span>
                </div>
                <div class="data-item">
                    <span class="data-label">今日发电量</span>
                    <span class="data-value">1,248 kWh</span>
                </div>
                <div class="data-item">
                    <span class="data-label">累计发电量</span>
                    <span class="data-value">2,456,789 kWh</span>
                </div>
                <div class="data-item">
                    <span class="data-label">环境温度</span>
                    <span class="data-value">28.5°C</span>
                </div>
                <div class="data-item">
                    <span class="data-label">风速</span>
                    <span class="data-value">3.2 m/s</span>
                </div>
                <div class="data-item">
                    <span class="data-label">湿度</span>
                    <span class="data-value">65%</span>
                </div>
            </div>
        </div>

        <div style="margin-bottom: 20px;">
            <h3 style="color: #2c3e50; margin-bottom: 15px;">光伏板组状态监控</h3>
        </div>

        <div class="panel-grid">
            <div class="panel-card">
                <div class="panel-header">
                    <div class="panel-id">光伏板组 #01</div>
                    <div class="panel-status status-online"></div>
                </div>
                <div class="panel-power">12.5 kW</div>
                <div class="panel-efficiency">效率: 22.3% | 温度: 44.1°C</div>
            </div>
            <div class="panel-card">
                <div class="panel-header">
                    <div class="panel-id">光伏板组 #02</div>
                    <div class="panel-status status-online"></div>
                </div>
                <div class="panel-power">11.8 kW</div>
                <div class="panel-efficiency">效率: 21.9% | 温度: 45.2°C</div>
            </div>
            <div class="panel-card">
                <div class="panel-header">
                    <div class="panel-id">光伏板组 #03</div>
                    <div class="panel-status status-warning"></div>
                </div>
                <div class="panel-power">10.2 kW</div>
                <div class="panel-efficiency">效率: 19.8% | 温度: 48.5°C</div>
            </div>
            <div class="panel-card">
                <div class="panel-header">
                    <div class="panel-id">光伏板组 #04</div>
                    <div class="panel-status status-online"></div>
                </div>
                <div class="panel-power">12.1 kW</div>
                <div class="panel-efficiency">效率: 22.1% | 温度: 43.8°C</div>
            </div>
            <div class="panel-card">
                <div class="panel-header">
                    <div class="panel-id">光伏板组 #05</div>
                    <div class="panel-status status-online"></div>
                </div>
                <div class="panel-power">11.9 kW</div>
                <div class="panel-efficiency">效率: 21.7% | 温度: 44.9°C</div>
            </div>
            <div class="panel-card">
                <div class="panel-header">
                    <div class="panel-id">光伏板组 #06</div>
                    <div class="panel-status status-online"></div>
                </div>
                <div class="panel-power">12.3 kW</div>
                <div class="panel-efficiency">效率: 22.5% | 温度: 43.2°C</div>
            </div>
        </div>
    </div>
</body>
</html>
