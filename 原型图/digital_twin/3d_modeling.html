<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D建模 - VPP-AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #e1705522 0%, #e1705544 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .title {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb a {
            color: #3498db;
            text-decoration: none;
            margin: 0 5px;
        }
        
        .content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            min-height: 500px;
        }
        
        .back-btn {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 20px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #e17055;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .feature-desc {
            color: #7f8c8d;
            line-height: 1.6;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #27ae60;
            margin-right: 8px;
        }
        
        .placeholder {
            text-align: center;
            color: #7f8c8d;
            font-size: 18px;
            margin: 50px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="title">
                <span>🏭</span>
                <span>3D建模</span>
            </h1>
            <p class="subtitle">三维建模管理</p>
            <div class="nav-breadcrumb">
                <a href="../index.html">🏠 首页</a> > 
                <a href="../dashboard/main.html">📊 控制台</a> > 
                <a href="index.html">🏭 数字孪生系统</a> > 
                <span>3D建模</span>
            </div>
        </div>
    </div>

    <a href="index.html" class="back-btn">← 返回数字孪生系统</a>

    <div class="content">
        <h2 style="color: #2c3e50; margin-bottom: 20px;">
            <span class="status-indicator"></span>3D建模
        </h2>
        
        <!-- 3D建模工具栏 -->
        <div style="background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <h3 style="color: #2c3e50; margin: 0;">🏗️ 3D建模工具</h3>
                <div style="display: flex; gap: 10px;">
                    <button style="background: #27ae60; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">💾 保存模型</button>
                    <button style="background: #3498db; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">📤 导出模型</button>
                    <button style="background: #f39c12; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">🔄 重置视图</button>
                </div>
            </div>

            <div style="display: flex; gap: 15px; align-items: center;">
                <div style="display: flex; gap: 8px;">
                    <button style="background: #e17055; color: white; border: none; padding: 6px 12px; border-radius: 3px; cursor: pointer; font-size: 12px;">🏠 添加建筑</button>
                    <button style="background: #fdcb6e; color: white; border: none; padding: 6px 12px; border-radius: 3px; cursor: pointer; font-size: 12px;">🌞 添加光伏板</button>
                    <button style="background: #74b9ff; color: white; border: none; padding: 6px 12px; border-radius: 3px; cursor: pointer; font-size: 12px;">🔋 添加储能</button>
                    <button style="background: #a29bfe; color: white; border: none; padding: 6px 12px; border-radius: 3px; cursor: pointer; font-size: 12px;">⚡ 添加负荷</button>
                </div>

                <div style="border-left: 1px solid #ddd; padding-left: 15px; display: flex; gap: 8px;">
                    <button style="background: #636e72; color: white; border: none; padding: 6px 12px; border-radius: 3px; cursor: pointer; font-size: 12px;">🎯 选择</button>
                    <button style="background: #636e72; color: white; border: none; padding: 6px 12px; border-radius: 3px; cursor: pointer; font-size: 12px;">↔️ 移动</button>
                    <button style="background: #636e72; color: white; border: none; padding: 6px 12px; border-radius: 3px; cursor: pointer; font-size: 12px;">🔄 旋转</button>
                    <button style="background: #636e72; color: white; border: none; padding: 6px 12px; border-radius: 3px; cursor: pointer; font-size: 12px;">📏 缩放</button>
                </div>

                <div style="border-left: 1px solid #ddd; padding-left: 15px;">
                    <label style="font-size: 12px; color: #7f8c8d;">视图模式:</label>
                    <select style="margin-left: 5px; padding: 4px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                        <option>透视视图</option>
                        <option>正交视图</option>
                        <option>俯视图</option>
                        <option>侧视图</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div style="display: grid; grid-template-columns: 1fr 300px; gap: 20px;">
            <!-- 3D视图区域 -->
            <div class="feature-card" style="min-height: 600px;">
                <div class="feature-title">🏭 3D场景视图</div>
                <div style="height: 550px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; margin-top: 15px; position: relative; overflow: hidden;">
                    <!-- 模拟3D场景 -->
                    <div style="position: absolute; top: 20px; left: 20px; background: rgba(0,0,0,0.3); padding: 10px; border-radius: 5px; font-size: 12px;">
                        <div>📍 坐标: X:0, Y:0, Z:0</div>
                        <div>🔍 缩放: 100%</div>
                        <div>🎯 选中: 光伏板组#1</div>
                    </div>

                    <!-- 模拟建筑物 -->
                    <div style="position: absolute; bottom: 100px; left: 100px; width: 120px; height: 80px; background: rgba(255,255,255,0.2); border: 2px solid white; border-radius: 5px; display: flex; align-items: center; justify-content: center; font-size: 12px;">
                        🏢 主建筑
                    </div>

                    <!-- 模拟光伏板 -->
                    <div style="position: absolute; top: 80px; right: 120px; width: 100px; height: 60px; background: rgba(253, 203, 110, 0.8); border: 2px solid #fdcb6e; border-radius: 3px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #2c3e50;">
                        🌞 光伏板组
                    </div>

                    <!-- 模拟储能设备 -->
                    <div style="position: absolute; bottom: 120px; right: 100px; width: 60px; height: 40px; background: rgba(116, 185, 255, 0.8); border: 2px solid #74b9ff; border-radius: 5px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #2c3e50;">
                        🔋
                    </div>

                    <div style="text-align: center;">
                        <div style="font-size: 24px; margin-bottom: 10px;">🏭</div>
                        <div style="font-size: 18px; margin-bottom: 5px;">VPP-AI 数字孪生场景</div>
                        <div style="font-size: 14px; opacity: 0.8;">3D建模与仿真环境</div>
                        <div style="font-size: 12px; margin-top: 10px; opacity: 0.6;">
                            使用鼠标拖拽旋转视角，滚轮缩放，右键平移
                        </div>
                    </div>

                    <!-- 视图控制器 -->
                    <div style="position: absolute; bottom: 20px; right: 20px; display: flex; flex-direction: column; gap: 5px;">
                        <button style="width: 40px; height: 30px; background: rgba(0,0,0,0.3); color: white; border: 1px solid white; border-radius: 3px; cursor: pointer; font-size: 12px;">↑</button>
                        <div style="display: flex; gap: 5px;">
                            <button style="width: 30px; height: 30px; background: rgba(0,0,0,0.3); color: white; border: 1px solid white; border-radius: 3px; cursor: pointer; font-size: 12px;">←</button>
                            <button style="width: 30px; height: 30px; background: rgba(0,0,0,0.3); color: white; border: 1px solid white; border-radius: 3px; cursor: pointer; font-size: 12px;">→</button>
                        </div>
                        <button style="width: 40px; height: 30px; background: rgba(0,0,0,0.3); color: white; border: 1px solid white; border-radius: 3px; cursor: pointer; font-size: 12px;">↓</button>
                    </div>
                </div>
            </div>

            <!-- 右侧面板 -->
            <div style="display: flex; flex-direction: column; gap: 20px;">
                <!-- 场景树 -->
                <div class="feature-card">
                    <div class="feature-title">🌳 场景层次结构</div>
                    <div style="margin-top: 15px; font-size: 14px;">
                        <div style="margin-bottom: 10px;">
                            <div style="font-weight: bold; margin-bottom: 5px; cursor: pointer;">📁 VPP系统场景</div>
                            <div style="margin-left: 20px;">
                                <div style="margin-bottom: 3px; cursor: pointer; padding: 2px; border-radius: 3px;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='transparent'">🏢 主建筑</div>
                                <div style="margin-bottom: 3px; cursor: pointer; padding: 2px; border-radius: 3px; background: #e3f2fd;" onmouseover="this.style.background='#bbdefb'" onmouseout="this.style.background='#e3f2fd'">🌞 光伏板组#1 (选中)</div>
                                <div style="margin-left: 20px; margin-bottom: 3px; cursor: pointer; padding: 2px; border-radius: 3px;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='transparent'">📋 光伏板#1-1</div>
                                <div style="margin-left: 20px; margin-bottom: 3px; cursor: pointer; padding: 2px; border-radius: 3px;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='transparent'">📋 光伏板#1-2</div>
                                <div style="margin-bottom: 3px; cursor: pointer; padding: 2px; border-radius: 3px;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='transparent'">🔋 储能系统#1</div>
                                <div style="margin-bottom: 3px; cursor: pointer; padding: 2px; border-radius: 3px;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='transparent'">⚡ 负荷设备组</div>
                                <div style="margin-left: 20px; margin-bottom: 3px; cursor: pointer; padding: 2px; border-radius: 3px;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='transparent'">🌡️ 中央空调</div>
                                <div style="margin-left: 20px; margin-bottom: 3px; cursor: pointer; padding: 2px; border-radius: 3px;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='transparent'">💡 照明系统</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 属性面板 -->
                <div class="feature-card">
                    <div class="feature-title">⚙️ 对象属性</div>
                    <div style="margin-top: 15px; font-size: 14px;">
                        <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                            <div style="font-weight: bold; margin-bottom: 5px;">🌞 光伏板组#1</div>
                            <div style="font-size: 12px; color: #7f8c8d;">选中对象的详细属性</div>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; font-weight: bold; margin-bottom: 5px;">位置坐标:</label>
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 5px;">
                                <input type="number" value="10.5" step="0.1" style="padding: 4px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                                <input type="number" value="5.2" step="0.1" style="padding: 4px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                                <input type="number" value="3.0" step="0.1" style="padding: 4px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 5px; font-size: 10px; color: #7f8c8d; margin-top: 2px;">
                                <span>X (m)</span>
                                <span>Y (m)</span>
                                <span>Z (m)</span>
                            </div>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; font-weight: bold; margin-bottom: 5px;">旋转角度:</label>
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 5px;">
                                <input type="number" value="0" step="1" style="padding: 4px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                                <input type="number" value="30" step="1" style="padding: 4px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                                <input type="number" value="180" step="1" style="padding: 4px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 5px; font-size: 10px; color: #7f8c8d; margin-top: 2px;">
                                <span>俯仰 (°)</span>
                                <span>倾斜 (°)</span>
                                <span>方位 (°)</span>
                            </div>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; font-weight: bold; margin-bottom: 5px;">技术参数:</label>
                            <div style="font-size: 12px; color: #7f8c8d;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
                                    <span>额定功率:</span>
                                    <span>200 kW</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
                                    <span>面积:</span>
                                    <span>1000 m²</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
                                    <span>效率:</span>
                                    <span>22.1%</span>
                                </div>
                                <div style="display: flex; justify-content: space-between;">
                                    <span>状态:</span>
                                    <span style="color: #27ae60;">正常运行</span>
                                </div>
                            </div>
                        </div>

                        <button style="width: 100%; background: #3498db; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer; font-size: 12px;">📝 应用修改</button>
                    </div>
                </div>

                <!-- 材质库 -->
                <div class="feature-card">
                    <div class="feature-title">🎨 材质库</div>
                    <div style="margin-top: 15px;">
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px;">
                            <div style="width: 100%; height: 40px; background: linear-gradient(45deg, #3498db, #2980b9); border-radius: 5px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: white; font-size: 10px;">光伏板</div>
                            <div style="width: 100%; height: 40px; background: linear-gradient(45deg, #95a5a6, #7f8c8d); border-radius: 5px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: white; font-size: 10px;">金属</div>
                            <div style="width: 100%; height: 40px; background: linear-gradient(45deg, #e74c3c, #c0392b); border-radius: 5px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: white; font-size: 10px;">砖墙</div>
                            <div style="width: 100%; height: 40px; background: linear-gradient(45deg, #f39c12, #e67e22); border-radius: 5px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: white; font-size: 10px;">混凝土</div>
                            <div style="width: 100%; height: 40px; background: linear-gradient(45deg, #27ae60, #229954); border-radius: 5px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: white; font-size: 10px;">草地</div>
                            <div style="width: 100%; height: 40px; background: linear-gradient(45deg, #8e44ad, #7d3c98); border-radius: 5px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: white; font-size: 10px;">玻璃</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div style="text-align: center; padding: 40px; color: rgba(0,0,0,0.6);">
        <p>© 2025 VPP-AI 数字孪生系统 - 3D建模</p>
        <p>原型界面展示，实际功能正在开发中</p>
    </div>
</body>
</html>