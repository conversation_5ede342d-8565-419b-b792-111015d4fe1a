<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化策略 - VPP-AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #fd79a822 0%, #fd79a844 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .title {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb a {
            color: #3498db;
            text-decoration: none;
            margin: 0 5px;
        }
        
        .content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            min-height: 500px;
        }
        
        .back-btn {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 20px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #fd79a8;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .feature-desc {
            color: #7f8c8d;
            line-height: 1.6;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #27ae60;
            margin-right: 8px;
        }
        
        .placeholder {
            text-align: center;
            color: #7f8c8d;
            font-size: 18px;
            margin: 50px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="title">
                <span>🧠</span>
                <span>优化策略</span>
            </h1>
            <p class="subtitle">优化策略管理</p>
            <div class="nav-breadcrumb">
                <a href="../index.html">🏠 首页</a> > 
                <a href="../dashboard/main.html">📊 控制台</a> > 
                <a href="index.html">🧠 智能优化引擎</a> > 
                <span>优化策略</span>
            </div>
        </div>
    </div>

    <a href="index.html" class="back-btn">← 返回智能优化引擎</a>

    <div class="content">
        <h2 style="color: #2c3e50; margin-bottom: 20px;">
            <span class="status-indicator"></span>优化策略
        </h2>
        
        <!-- 策略概览 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">
            <div class="feature-card" style="text-align: center; border-left: 4px solid #fd79a8;">
                <div style="font-size: 28px; font-weight: bold; color: #fd79a8; margin-bottom: 5px;">收益最大化</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">当前策略</div>
                <div style="font-size: 12px; color: #00b894;">🚀 运行中</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #00b894;">
                <div style="font-size: 28px; font-weight: bold; color: #00b894; margin-bottom: 5px;">+18.5%</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">预期收益提升</div>
                <div style="font-size: 12px; color: #00b894;">📈 较基准策略</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #6c5ce7;">
                <div style="font-size: 28px; font-weight: bold; color: #6c5ce7; margin-bottom: 5px;">94.2%</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">策略成功率</div>
                <div style="font-size: 12px; color: #00b894;">✅ 过去30天</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #fdcb6e;">
                <div style="font-size: 28px; font-weight: bold; color: #fdcb6e; margin-bottom: 5px;">15分钟</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">优化周期</div>
                <div style="font-size: 12px; color: #00b894;">⏱️ 实时优化</div>
            </div>
        </div>

        <!-- 策略配置 -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
            <div class="feature-card">
                <div class="feature-title">🎯 优化目标配置</div>
                <div style="margin-top: 15px;">
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; font-weight: bold; margin-bottom: 10px;">主要目标:</label>
                        <select style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 5px; margin-bottom: 10px;">
                            <option selected>经济收益最大化</option>
                            <option>成本最小化</option>
                            <option>碳排放最小化</option>
                            <option>系统稳定性最大化</option>
                        </select>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; font-weight: bold; margin-bottom: 10px;">权重分配:</label>
                        <div style="margin-bottom: 10px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                <span>经济效益:</span>
                                <span style="font-weight: bold;">70%</span>
                            </div>
                            <input type="range" min="0" max="100" value="70" style="width: 100%;">
                        </div>
                        <div style="margin-bottom: 10px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                <span>系统稳定:</span>
                                <span style="font-weight: bold;">20%</span>
                            </div>
                            <input type="range" min="0" max="100" value="20" style="width: 100%;">
                        </div>
                        <div style="margin-bottom: 10px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                <span>环保指标:</span>
                                <span style="font-weight: bold;">10%</span>
                            </div>
                            <input type="range" min="0" max="100" value="10" style="width: 100%;">
                        </div>
                    </div>

                    <button style="width: 100%; background: #fd79a8; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">💾 保存目标配置</button>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-title">⚙️ 约束条件设置</div>
                <div style="margin-top: 15px;">
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; font-weight: bold; margin-bottom: 5px;">电池SOC范围:</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <input type="number" value="10" min="0" max="100" style="width: 60px; padding: 5px; border: 1px solid #ddd; border-radius: 3px;">
                            <span>% -</span>
                            <input type="number" value="90" min="0" max="100" style="width: 60px; padding: 5px; border: 1px solid #ddd; border-radius: 3px;">
                            <span>%</span>
                        </div>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; font-weight: bold; margin-bottom: 5px;">最大充放电功率:</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <input type="number" value="100" style="width: 80px; padding: 5px; border: 1px solid #ddd; border-radius: 3px;">
                            <span>kW</span>
                        </div>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; font-weight: bold; margin-bottom: 5px;">负荷优先级保护:</label>
                        <div style="display: flex; flex-direction: column; gap: 5px;">
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox" checked>
                                <span>关键负荷不可中断</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox" checked>
                                <span>高优先级负荷优先保障</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox">
                                <span>允许低优先级负荷调节</span>
                            </label>
                        </div>
                    </div>

                    <button style="width: 100%; background: #6c5ce7; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">🔒 应用约束条件</button>
                </div>
            </div>
        </div>

        <!-- 策略执行状态 -->
        <div class="feature-card">
            <div class="feature-title">📊 策略执行状态与结果</div>
            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin-top: 15px;">
                <div>
                    <div style="height: 250px; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #6c757d; margin-bottom: 15px;">
                        📊 优化策略执行效果图
                        <br>显示实际收益与预期收益的对比
                        <br>以及各项指标的达成情况
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px;">
                        <div style="background: #e8f5e8; padding: 10px; border-radius: 5px; text-align: center;">
                            <div style="font-weight: bold; color: #27ae60;">实际收益</div>
                            <div style="font-size: 18px; color: #27ae60;">¥2,456</div>
                        </div>
                        <div style="background: #e3f2fd; padding: 10px; border-radius: 5px; text-align: center;">
                            <div style="font-weight: bold; color: #2196f3;">预期收益</div>
                            <div style="font-size: 18px; color: #2196f3;">¥2,380</div>
                        </div>
                        <div style="background: #fff3cd; padding: 10px; border-radius: 5px; text-align: center;">
                            <div style="font-weight: bold; color: #856404;">收益率</div>
                            <div style="font-size: 18px; color: #856404;">+3.2%</div>
                        </div>
                        <div style="background: #f8d7da; padding: 10px; border-radius: 5px; text-align: center;">
                            <div style="font-weight: bold; color: #721c24;">偏差</div>
                            <div style="font-size: 18px; color: #721c24;">+3.2%</div>
                        </div>
                    </div>
                </div>

                <div>
                    <div style="background: #f8f9fa; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                        <div style="font-weight: bold; margin-bottom: 10px;">🎯 策略执行统计</div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>成功执行:</span>
                            <span style="font-weight: bold; color: #27ae60;">94.2%</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>部分执行:</span>
                            <span style="font-weight: bold; color: #f39c12;">4.1%</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>执行失败:</span>
                            <span style="font-weight: bold; color: #e74c3c;">1.7%</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>平均执行时间:</span>
                            <span style="font-weight: bold;">2.3秒</span>
                        </div>
                    </div>

                    <div style="display: flex; flex-direction: column; gap: 8px;">
                        <button style="background: #27ae60; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">🚀 立即执行优化</button>
                        <button style="background: #f39c12; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">⏸️ 暂停策略</button>
                        <button style="background: #6c5ce7; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">📋 查看详细日志</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div style="text-align: center; padding: 40px; color: rgba(0,0,0,0.6);">
        <p>© 2025 VPP-AI 智能优化引擎 - 优化策略</p>
        <p>原型界面展示，实际功能正在开发中</p>
    </div>
</body>
</html>