<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财务报表 - VPP-AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #a29bfe22 0%, #a29bfe44 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .title {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb a {
            color: #3498db;
            text-decoration: none;
            margin: 0 5px;
        }
        
        .content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            min-height: 500px;
        }
        
        .back-btn {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 20px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #a29bfe;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .feature-desc {
            color: #7f8c8d;
            line-height: 1.6;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #27ae60;
            margin-right: 8px;
        }
        
        .placeholder {
            text-align: center;
            color: #7f8c8d;
            font-size: 18px;
            margin: 50px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="title">
                <span>📈</span>
                <span>财务报表</span>
            </h1>
            <p class="subtitle">财务分析报表</p>
            <div class="nav-breadcrumb">
                <a href="../index.html">🏠 首页</a> > 
                <a href="../dashboard/main.html">📊 控制台</a> > 
                <a href="index.html">📈 报表分析</a> > 
                <span>财务报表</span>
            </div>
        </div>
    </div>

    <a href="index.html" class="back-btn">← 返回报表分析</a>

    <div class="content">
        <h2 style="color: #2c3e50; margin-bottom: 20px;">
            <span class="status-indicator"></span>财务报表
        </h2>
        
        <!-- 财务概览 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">
            <div class="feature-card" style="text-align: center; border-left: 4px solid #27ae60;">
                <div style="font-size: 28px; font-weight: bold; color: #27ae60; margin-bottom: 5px;">¥73,456</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">本月总收入</div>
                <div style="font-size: 12px; color: #27ae60;">📈 +12.5% 较上月</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #e74c3c;">
                <div style="font-size: 28px; font-weight: bold; color: #e74c3c; margin-bottom: 5px;">¥28,234</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">本月总支出</div>
                <div style="font-size: 12px; color: #e74c3c;">📊 +3.2% 较上月</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #3498db;">
                <div style="font-size: 28px; font-weight: bold; color: #3498db; margin-bottom: 5px;">¥45,222</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">本月净利润</div>
                <div style="font-size: 12px; color: #27ae60;">💰 +18.7% 较上月</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #f39c12;">
                <div style="font-size: 28px; font-weight: bold; color: #f39c12; margin-bottom: 5px;">15.2%</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">投资回报率</div>
                <div style="font-size: 12px; color: #27ae60;">📈 年化收益率</div>
            </div>
        </div>

        <!-- 收支分析 -->
        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin-bottom: 30px;">
            <div class="feature-card">
                <div class="feature-title">📊 月度收支趋势分析</div>
                <div style="height: 300px; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #6c757d; margin-top: 15px;">
                    📈 收支趋势图表
                    <br>显示过去12个月的收入、支出和净利润变化
                    <br>包含同比、环比分析
                </div>
                <div style="display: flex; justify-content: space-between; margin-top: 10px; font-size: 12px; color: #7f8c8d;">
                    <span>🟢 总收入</span>
                    <span>🔴 总支出</span>
                    <span>🔵 净利润</span>
                    <span>🟡 预测值</span>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-title">💰 收入构成分析</div>
                <div style="margin-top: 15px;">
                    <div style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span style="font-size: 14px;">售电收入</span>
                            <span style="font-weight: bold; color: #27ae60;">¥52,340</span>
                        </div>
                        <div style="background: #eee; height: 8px; border-radius: 4px;">
                            <div style="background: #27ae60; height: 8px; width: 71%; border-radius: 4px;"></div>
                        </div>
                        <div style="font-size: 10px; color: #7f8c8d; margin-top: 2px;">71.3% 占比</div>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span style="font-size: 14px;">辅助服务</span>
                            <span style="font-weight: bold; color: #3498db;">¥15,680</span>
                        </div>
                        <div style="background: #eee; height: 8px; border-radius: 4px;">
                            <div style="background: #3498db; height: 8px; width: 21%; border-radius: 4px;"></div>
                        </div>
                        <div style="font-size: 10px; color: #7f8c8d; margin-top: 2px;">21.4% 占比</div>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span style="font-size: 14px;">需求响应</span>
                            <span style="font-weight: bold; color: #f39c12;">¥3,890</span>
                        </div>
                        <div style="background: #eee; height: 8px; border-radius: 4px;">
                            <div style="background: #f39c12; height: 8px; width: 5%; border-radius: 4px;"></div>
                        </div>
                        <div style="font-size: 10px; color: #7f8c8d; margin-top: 2px;">5.3% 占比</div>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span style="font-size: 14px;">其他收入</span>
                            <span style="font-weight: bold; color: #a29bfe;">¥1,546</span>
                        </div>
                        <div style="background: #eee; height: 8px; border-radius: 4px;">
                            <div style="background: #a29bfe; height: 8px; width: 2%; border-radius: 4px;"></div>
                        </div>
                        <div style="font-size: 10px; color: #7f8c8d; margin-top: 2px;">2.1% 占比</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细财务报表 -->
        <div class="feature-card">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <div class="feature-title">📋 详细财务报表</div>
                <div style="display: flex; gap: 10px;">
                    <select style="padding: 6px; border: 1px solid #ddd; border-radius: 5px;">
                        <option>2025年6月</option>
                        <option>2025年5月</option>
                        <option>2025年4月</option>
                    </select>
                    <button style="background: #27ae60; color: white; border: none; padding: 6px 12px; border-radius: 5px; cursor: pointer;">📤 导出Excel</button>
                    <button style="background: #e74c3c; color: white; border: none; padding: 6px 12px; border-radius: 5px; cursor: pointer;">📄 生成PDF</button>
                </div>
            </div>

            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                    <thead>
                        <tr style="background: #f8f9fa;">
                            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #ddd; font-weight: bold;">项目</th>
                            <th style="padding: 12px; text-align: right; border-bottom: 2px solid #ddd; font-weight: bold;">本月金额(元)</th>
                            <th style="padding: 12px; text-align: right; border-bottom: 2px solid #ddd; font-weight: bold;">上月金额(元)</th>
                            <th style="padding: 12px; text-align: right; border-bottom: 2px solid #ddd; font-weight: bold;">同比变化</th>
                            <th style="padding: 12px; text-align: right; border-bottom: 2px solid #ddd; font-weight: bold;">占比</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="background: #e8f5e8;">
                            <td style="padding: 10px; border-bottom: 1px solid #eee; font-weight: bold; color: #27ae60;">收入合计</td>
                            <td style="padding: 10px; text-align: right; border-bottom: 1px solid #eee; font-weight: bold; color: #27ae60;">73,456</td>
                            <td style="padding: 10px; text-align: right; border-bottom: 1px solid #eee;">65,234</td>
                            <td style="padding: 10px; text-align: right; border-bottom: 1px solid #eee; color: #27ae60;">+12.6%</td>
                            <td style="padding: 10px; text-align: right; border-bottom: 1px solid #eee;">100.0%</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; padding-left: 20px; border-bottom: 1px solid #eee;">售电收入</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">52,340</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">46,890</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee; color: #27ae60;">+11.6%</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">71.3%</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; padding-left: 20px; border-bottom: 1px solid #eee;">辅助服务收入</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">15,680</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">13,450</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee; color: #27ae60;">+16.6%</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">21.4%</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; padding-left: 20px; border-bottom: 1px solid #eee;">需求响应收入</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">3,890</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">3,234</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee; color: #27ae60;">+20.3%</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">5.3%</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; padding-left: 20px; border-bottom: 1px solid #eee;">其他收入</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">1,546</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">1,660</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee; color: #e74c3c;">-6.9%</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">2.1%</td>
                        </tr>
                        <tr style="background: #f8d7da;">
                            <td style="padding: 10px; border-bottom: 1px solid #eee; font-weight: bold; color: #721c24;">支出合计</td>
                            <td style="padding: 10px; text-align: right; border-bottom: 1px solid #eee; font-weight: bold; color: #721c24;">28,234</td>
                            <td style="padding: 10px; text-align: right; border-bottom: 1px solid #eee;">27,356</td>
                            <td style="padding: 10px; text-align: right; border-bottom: 1px solid #eee; color: #e74c3c;">+3.2%</td>
                            <td style="padding: 10px; text-align: right; border-bottom: 1px solid #eee;">38.4%</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; padding-left: 20px; border-bottom: 1px solid #eee;">运维费用</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">12,450</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">11,890</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee; color: #e74c3c;">+4.7%</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">16.9%</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; padding-left: 20px; border-bottom: 1px solid #eee;">设备折旧</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">8,760</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">8,760</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee; color: #7f8c8d;">0.0%</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">11.9%</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; padding-left: 20px; border-bottom: 1px solid #eee;">购电费用</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">4,560</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">4,230</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee; color: #e74c3c;">+7.8%</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">6.2%</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; padding-left: 20px; border-bottom: 1px solid #eee;">其他费用</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">2,464</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">2,476</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee; color: #27ae60;">-0.5%</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #eee;">3.4%</td>
                        </tr>
                        <tr style="background: #e3f2fd;">
                            <td style="padding: 12px; border-bottom: 2px solid #ddd; font-weight: bold; color: #1976d2; font-size: 16px;">净利润</td>
                            <td style="padding: 12px; text-align: right; border-bottom: 2px solid #ddd; font-weight: bold; color: #1976d2; font-size: 16px;">45,222</td>
                            <td style="padding: 12px; text-align: right; border-bottom: 2px solid #ddd;">37,878</td>
                            <td style="padding: 12px; text-align: right; border-bottom: 2px solid #ddd; color: #27ae60; font-weight: bold;">+19.4%</td>
                            <td style="padding: 12px; text-align: right; border-bottom: 2px solid #ddd;">61.6%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div style="text-align: center; padding: 40px; color: rgba(0,0,0,0.6);">
        <p>© 2025 VPP-AI 报表分析 - 财务报表</p>
        <p>原型界面展示，实际功能正在开发中</p>
    </div>
</body>
</html>