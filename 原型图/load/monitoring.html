<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时监控 - VPP-AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #00b89422 0%, #00b89444 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .title {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb a {
            color: #3498db;
            text-decoration: none;
            margin: 0 5px;
        }
        
        .content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            min-height: 500px;
        }
        
        .back-btn {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 20px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #00b894;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .feature-desc {
            color: #7f8c8d;
            line-height: 1.6;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #27ae60;
            margin-right: 8px;
        }
        
        .placeholder {
            text-align: center;
            color: #7f8c8d;
            font-size: 18px;
            margin: 50px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="title">
                <span>⚡</span>
                <span>实时监控</span>
            </h1>
            <p class="subtitle">负荷实时监控</p>
            <div class="nav-breadcrumb">
                <a href="../index.html">🏠 首页</a> > 
                <a href="../dashboard/main.html">📊 控制台</a> > 
                <a href="index.html">⚡ 负荷管理系统</a> > 
                <span>实时监控</span>
            </div>
        </div>
    </div>

    <a href="index.html" class="back-btn">← 返回负荷管理系统</a>

    <div class="content">
        <h2 style="color: #2c3e50; margin-bottom: 20px;">
            <span class="status-indicator"></span>实时监控
        </h2>
        
        <!-- 负荷概览 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">
            <div class="feature-card" style="text-align: center; border-left: 4px solid #00b894;">
                <div style="font-size: 28px; font-weight: bold; color: #00b894; margin-bottom: 5px;">89.2 kW</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">当前总负荷</div>
                <div style="font-size: 12px; color: #00b894;">📈 负荷率 67%</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #6c5ce7;">
                <div style="font-size: 28px; font-weight: bold; color: #6c5ce7; margin-bottom: 5px;">125.6 kW</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">峰值负荷</div>
                <div style="font-size: 12px; color: #7f8c8d;">今日 14:30</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #fd79a8;">
                <div style="font-size: 28px; font-weight: bold; color: #fd79a8; margin-bottom: 5px;">15.2 kW</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">可调节负荷</div>
                <div style="font-size: 12px; color: #00b894;">💡 灵活性 12%</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #fdcb6e;">
                <div style="font-size: 28px; font-weight: bold; color: #fdcb6e; margin-bottom: 5px;">¥1,245</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">今日电费</div>
                <div style="font-size: 12px; color: #e74c3c;">📊 较昨日 +5.2%</div>
            </div>
        </div>

        <!-- 负荷设备状态 -->
        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin-bottom: 30px;">
            <div class="feature-card">
                <div class="feature-title">⚡ 负荷设备实时状态</div>
                <div style="margin-top: 15px;">
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
                        <div style="background: #e8f5e8; border-radius: 8px; padding: 15px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <span style="font-weight: bold; color: #27ae60;">🌡️ 中央空调</span>
                                <span style="background: #27ae60; color: white; padding: 2px 8px; border-radius: 10px; font-size: 12px;">运行中</span>
                            </div>
                            <div style="font-size: 20px; font-weight: bold; color: #27ae60; margin-bottom: 5px;">45.8 kW</div>
                            <div style="font-size: 12px; color: #7f8c8d;">设定温度: 24°C | 优先级: 高</div>
                        </div>
                        <div style="background: #e8f5e8; border-radius: 8px; padding: 15px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <span style="font-weight: bold; color: #27ae60;">💡 照明系统</span>
                                <span style="background: #27ae60; color: white; padding: 2px 8px; border-radius: 10px; font-size: 12px;">运行中</span>
                            </div>
                            <div style="font-size: 20px; font-weight: bold; color: #27ae60; margin-bottom: 5px;">12.3 kW</div>
                            <div style="font-size: 12px; color: #7f8c8d;">亮度: 80% | 优先级: 中</div>
                        </div>
                        <div style="background: #fff3cd; border-radius: 8px; padding: 15px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <span style="font-weight: bold; color: #856404;">🏭 生产设备</span>
                                <span style="background: #ffc107; color: white; padding: 2px 8px; border-radius: 10px; font-size: 12px;">调节中</span>
                            </div>
                            <div style="font-size: 20px; font-weight: bold; color: #856404; margin-bottom: 5px;">28.5 kW</div>
                            <div style="font-size: 12px; color: #7f8c8d;">负荷率: 75% | 优先级: 最高</div>
                        </div>
                        <div style="background: #f8d7da; border-radius: 8px; padding: 15px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <span style="font-weight: bold; color: #721c24;">🚗 充电桩</span>
                                <span style="background: #dc3545; color: white; padding: 2px 8px; border-radius: 10px; font-size: 12px;">已暂停</span>
                            </div>
                            <div style="font-size: 20px; font-weight: bold; color: #721c24; margin-bottom: 5px;">0 kW</div>
                            <div style="font-size: 12px; color: #7f8c8d;">需求响应暂停 | 优先级: 低</div>
                        </div>
                    </div>
                </div>
                <div style="height: 200px; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #6c757d; margin-top: 15px;">
                    📊 负荷功率实时曲线
                    <br>显示各类负荷的功率变化趋势
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-title">🎛️ 负荷控制面板</div>
                <div style="margin-top: 15px;">
                    <div style="margin-bottom: 20px;">
                        <div style="font-weight: bold; margin-bottom: 10px;">🌡️ 空调控制</div>
                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                            <span style="font-size: 14px;">温度:</span>
                            <input type="range" min="20" max="28" value="24" style="flex: 1;">
                            <span style="font-weight: bold;">24°C</span>
                        </div>
                        <button style="width: 100%; background: #27ae60; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">应用设置</button>
                    </div>
                    
                    <div style="margin-bottom: 20px;">
                        <div style="font-weight: bold; margin-bottom: 10px;">💡 照明控制</div>
                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                            <span style="font-size: 14px;">亮度:</span>
                            <input type="range" min="0" max="100" value="80" style="flex: 1;">
                            <span style="font-weight: bold;">80%</span>
                        </div>
                        <button style="width: 100%; background: #f39c12; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">调节亮度</button>
                    </div>
                    
                    <div style="margin-bottom: 20px;">
                        <div style="font-weight: bold; margin-bottom: 10px;">🚗 充电桩控制</div>
                        <button style="width: 100%; background: #27ae60; color: white; border: none; padding: 8px; border-radius: 5px; margin-bottom: 5px; cursor: pointer;">恢复充电</button>
                        <button style="width: 100%; background: #e74c3c; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">延迟充电</button>
                    </div>
                </div>
            </div>
        </div>
                <div class="feature-desc">实时数据图表和统计信息展示</div>
            </div>
            <div class="feature-card">
                <div class="feature-title">⚙️ 功能控制</div>
                <div class="feature-desc">相关功能的操作控制界面</div>
            </div>
            <div class="feature-card">
                <div class="feature-title">📈 趋势分析</div>
                <div class="feature-desc">历史数据趋势分析和预测</div>
            </div>
            <div class="feature-card">
                <div class="feature-title">🔔 告警提醒</div>
                <div class="feature-desc">相关告警信息和处理建议</div>
            </div>
        </div>
    </div>

    <div style="text-align: center; padding: 40px; color: rgba(0,0,0,0.6);">
        <p>© 2025 VPP-AI 负荷管理系统 - 实时监控</p>
        <p>原型界面展示，实际功能正在开发中</p>
    </div>
</body>
</html>