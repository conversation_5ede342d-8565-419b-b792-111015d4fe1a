<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>储能管理系统 - VPP-AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .title {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb a {
            color: #3498db;
            text-decoration: none;
            margin: 0 5px;
        }
        
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 30px;
            padding: 20px;
        }
        
        .nav-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }
        
        .nav-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #74b9ff, #0984e3);
        }
        
        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
            border-color: #74b9ff;
        }
        
        .nav-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
        }
        
        .nav-card .icon {
            font-size: 24px;
            margin-right: 10px;
        }
        
        .nav-card p {
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 15px;
            font-size: 14px;
        }
        
        .status-badge {
            background: #27ae60;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            display: inline-block;
        }
        
        .status-badge.warning {
            background: #f39c12;
        }
        
        .status-badge.charging {
            background: #3498db;
        }
        
        .status-badge.discharging {
            background: #e74c3c;
        }
        
        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 28px;
            font-weight: bold;
            color: #74b9ff;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-size: 14px;
        }
        
        .back-btn {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 20px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .battery-overview {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .battery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .battery-unit {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .battery-unit.charging {
            border-color: #3498db;
            background: #e3f2fd;
        }
        
        .battery-unit.discharging {
            border-color: #e74c3c;
            background: #ffebee;
        }
        
        .battery-unit.idle {
            border-color: #95a5a6;
            background: #ecf0f1;
        }
        
        .battery-soc {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .battery-status {
            font-size: 12px;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="title">
                <span>🔋</span>
                <span>储能管理系统</span>
            </h1>
            <p class="subtitle">Energy Storage Management System</p>
            <div class="nav-breadcrumb">
                <a href="../index.html">🏠 首页</a> > 
                <a href="../dashboard/main.html">📊 控制台</a> > 
                <span>🔋 储能系统</span>
            </div>
        </div>
    </div>

    <a href="../dashboard/main.html" class="back-btn">← 返回控制台</a>

    <div class="quick-stats">
        <div class="stat-card">
            <div class="stat-number">18.5%</div>
            <div class="stat-label">平均SOC</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">85.2 kW</div>
            <div class="stat-label">当前充电功率</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">500 kWh</div>
            <div class="stat-label">总容量</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">92.5 kWh</div>
            <div class="stat-label">可用容量</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">95.2%</div>
            <div class="stat-label">充电效率</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">2,456</div>
            <div class="stat-label">循环次数</div>
        </div>
    </div>

    <div class="battery-overview">
        <h3 style="color: #2c3e50; margin-bottom: 15px;">🔋 电池组状态总览</h3>
        <div class="battery-grid">
            <div class="battery-unit charging">
                <div class="battery-soc" style="color: #3498db;">25%</div>
                <div class="battery-status">电池组 #1 - 充电中</div>
            </div>
            <div class="battery-unit charging">
                <div class="battery-soc" style="color: #3498db;">18%</div>
                <div class="battery-status">电池组 #2 - 充电中</div>
            </div>
            <div class="battery-unit charging">
                <div class="battery-soc" style="color: #3498db;">22%</div>
                <div class="battery-status">电池组 #3 - 充电中</div>
            </div>
            <div class="battery-unit idle">
                <div class="battery-soc" style="color: #95a5a6;">12%</div>
                <div class="battery-status">电池组 #4 - 待机</div>
            </div>
            <div class="battery-unit charging">
                <div class="battery-soc" style="color: #3498db;">15%</div>
                <div class="battery-status">电池组 #5 - 充电中</div>
            </div>
        </div>
    </div>

    <div class="nav-grid">
        <div class="nav-card" onclick="window.open('monitoring.html', '_blank')">
            <h3><span class="icon">📊</span>实时监控</h3>
            <p>电池系统实时状态监控，包括SOC、电压、电流、温度、功率等关键参数的实时显示</p>
            <span class="status-badge charging">充电中</span>
        </div>

        <div class="nav-card" onclick="window.open('battery_management.html', '_blank')">
            <h3><span class="icon">🔧</span>电池管理</h3>
            <p>电池设备信息管理，包括电池档案、技术参数、安装信息、维护记录等</p>
            <span class="status-badge">正常</span>
        </div>

        <div class="nav-card" onclick="window.open('charge_control.html', '_blank')">
            <h3><span class="icon">⚡</span>充放电控制</h3>
            <p>电池充放电策略控制，包括手动控制、自动策略、功率限制、时间调度等</p>
            <span class="status-badge charging">运行中</span>
        </div>

        <div class="nav-card" onclick="window.open('soc_management.html', '_blank')">
            <h3><span class="icon">📈</span>SOC管理</h3>
            <p>电池荷电状态管理，包括SOC监控、SOC预测、均衡控制、容量校准等</p>
            <span class="status-badge">正常</span>
        </div>

        <div class="nav-card" onclick="window.open('thermal_management.html', '_blank')">
            <h3><span class="icon">🌡️</span>热管理系统</h3>
            <p>电池温度管理，包括温度监控、冷却控制、热平衡分析、温度预警等</p>
            <span class="status-badge">正常</span>
        </div>

        <div class="nav-card" onclick="window.open('safety_protection.html', '_blank')">
            <h3><span class="icon">🛡️</span>安全保护</h3>
            <p>电池安全保护系统，包括过压保护、过流保护、温度保护、绝缘监测等</p>
            <span class="status-badge">正常</span>
        </div>

        <div class="nav-card" onclick="window.open('life_analysis.html', '_blank')">
            <h3><span class="icon">📊</span>寿命分析</h3>
            <p>电池寿命评估和预测，包括循环寿命、日历寿命、容量衰减、健康状态评估</p>
            <span class="status-badge">正常</span>
        </div>

        <div class="nav-card" onclick="window.open('optimization.html', '_blank')">
            <h3><span class="icon">🧠</span>优化策略</h3>
            <p>储能系统优化策略，包括充放电优化、峰谷套利、需求响应、收益最大化</p>
            <span class="status-badge">运行中</span>
        </div>

        <div class="nav-card" onclick="window.open('maintenance.html', '_blank')">
            <h3><span class="icon">🔧</span>维护管理</h3>
            <p>电池维护计划和记录管理，包括定期检查、预防性维护、故障维修、备件管理</p>
            <span class="status-badge">正常</span>
        </div>

        <div class="nav-card" onclick="window.open('performance.html', '_blank')">
            <h3><span class="icon">🎯</span>性能评估</h3>
            <p>储能系统性能评估，包括效率分析、容量测试、性能对比、改进建议</p>
            <span class="status-badge">正常</span>
        </div>

        <div class="nav-card" onclick="window.open('alerts.html', '_blank')">
            <h3><span class="icon">🚨</span>告警管理</h3>
            <p>储能系统告警信息管理，包括实时告警、历史告警、告警规则、处理记录</p>
            <span class="status-badge warning">2条告警</span>
        </div>

        <div class="nav-card" onclick="window.open('energy_flow.html', '_blank')">
            <h3><span class="icon">🔄</span>能量流分析</h3>
            <p>储能系统能量流向分析，包括充放电流向、损耗分析、效率统计等</p>
            <span class="status-badge">正常</span>
        </div>

        <div class="nav-card" onclick="window.open('grid_services.html', '_blank')">
            <h3><span class="icon">🔌</span>电网服务</h3>
            <p>储能参与电网辅助服务，包括调频、调压、备用容量、黑启动等服务</p>
            <span class="status-badge">正常</span>
        </div>

        <div class="nav-card" onclick="window.open('economics.html', '_blank')">
            <h3><span class="icon">💰</span>经济分析</h3>
            <p>储能系统经济性分析，包括投资回报、运营成本、收益分析、成本优化</p>
            <span class="status-badge">正常</span>
        </div>

        <div class="nav-card" onclick="window.open('forecasting.html', '_blank')">
            <h3><span class="icon">🔮</span>状态预测</h3>
            <p>电池状态预测，包括SOC预测、寿命预测、故障预测、维护预测等</p>
            <span class="status-badge">运行中</span>
        </div>

        <div class="nav-card" onclick="window.open('reports.html', '_blank')">
            <h3><span class="icon">📋</span>报表中心</h3>
            <p>储能系统各类报表生成，包括运行报告、性能报告、维护报告、财务报告</p>
            <span class="status-badge">正常</span>
        </div>

        <div class="nav-card" onclick="window.open('settings.html', '_blank')">
            <h3><span class="icon">⚙️</span>系统设置</h3>
            <p>储能系统参数配置，包括运行参数、保护参数、通信参数、告警设置</p>
            <span class="status-badge">正常</span>
        </div>

        <div class="nav-card" onclick="window.open('mobile.html', '_blank')">
            <h3><span class="icon">📱</span>移动端监控</h3>
            <p>移动设备适配的储能系统监控界面，支持手机、平板等移动设备访问</p>
            <span class="status-badge">正常</span>
        </div>
    </div>

    <div style="text-align: center; padding: 40px; color: rgba(0,0,0,0.6);">
        <p>© 2025 VPP-AI 储能管理系统平台</p>
        <p>点击任意功能卡片进入对应的详细界面</p>
    </div>
</body>
</html>
