<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时监控 - VPP-AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #74b9ff22 0%, #74b9ff44 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .title {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb a {
            color: #3498db;
            text-decoration: none;
            margin: 0 5px;
        }
        
        .content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            min-height: 500px;
        }
        
        .back-btn {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 20px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #74b9ff;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .feature-desc {
            color: #7f8c8d;
            line-height: 1.6;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #27ae60;
            margin-right: 8px;
        }
        
        .placeholder {
            text-align: center;
            color: #7f8c8d;
            font-size: 18px;
            margin: 50px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="title">
                <span>🔋</span>
                <span>实时监控</span>
            </h1>
            <p class="subtitle">电池状态实时监控</p>
            <div class="nav-breadcrumb">
                <a href="../index.html">🏠 首页</a> > 
                <a href="../dashboard/main.html">📊 控制台</a> > 
                <a href="index.html">🔋 储能管理系统</a> > 
                <span>实时监控</span>
            </div>
        </div>
    </div>

    <a href="index.html" class="back-btn">← 返回储能管理系统</a>

    <div class="content">
        <h2 style="color: #2c3e50; margin-bottom: 20px;">
            <span class="status-indicator"></span>实时监控
        </h2>
        
        <!-- 实时状态概览 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">
            <div class="feature-card" style="text-align: center; border-left: 4px solid #74b9ff;">
                <div style="font-size: 28px; font-weight: bold; color: #74b9ff; margin-bottom: 5px;">18.5%</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">平均SOC</div>
                <div style="font-size: 12px; color: #e74c3c;">⚠️ 低电量告警</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #00b894;">
                <div style="font-size: 28px; font-weight: bold; color: #00b894; margin-bottom: 5px;">85.2 kW</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">充电功率</div>
                <div style="font-size: 12px; color: #00b894;">🔋 快速充电中</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #fdcb6e;">
                <div style="font-size: 28px; font-weight: bold; color: #fdcb6e; margin-bottom: 5px;">42.3°C</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">平均温度</div>
                <div style="font-size: 12px; color: #00b894;">✅ 温度正常</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #6c5ce7;">
                <div style="font-size: 28px; font-weight: bold; color: #6c5ce7; margin-bottom: 5px;">95.2%</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">充电效率</div>
                <div style="font-size: 12px; color: #00b894;">📈 效率优良</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #fd79a8;">
                <div style="font-size: 28px; font-weight: bold; color: #fd79a8; margin-bottom: 5px;">2,456</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">循环次数</div>
                <div style="font-size: 12px; color: #00b894;">🔄 寿命良好</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #e17055;">
                <div style="font-size: 28px; font-weight: bold; color: #e17055; margin-bottom: 5px;">92.5 kWh</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">可用容量</div>
                <div style="font-size: 12px; color: #00b894;">💾 容量充足</div>
            </div>
        </div>

        <!-- 电池组状态监控 -->
        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin-bottom: 30px;">
            <div class="feature-card">
                <div class="feature-title">🔋 电池组实时状态</div>
                <div style="display: grid; grid-template-columns: repeat(5, 1fr); gap: 10px; margin-top: 15px;">
                    <div style="background: #e3f2fd; border: 2px solid #2196f3; border-radius: 8px; padding: 10px; text-align: center;">
                        <div style="font-weight: bold; color: #2196f3; margin-bottom: 5px;">电池组 #1</div>
                        <div style="font-size: 18px; font-weight: bold; color: #2196f3;">25%</div>
                        <div style="font-size: 12px; color: #7f8c8d;">充电中</div>
                        <div style="font-size: 12px; color: #7f8c8d;">41.2°C</div>
                    </div>
                    <div style="background: #e3f2fd; border: 2px solid #2196f3; border-radius: 8px; padding: 10px; text-align: center;">
                        <div style="font-weight: bold; color: #2196f3; margin-bottom: 5px;">电池组 #2</div>
                        <div style="font-size: 18px; font-weight: bold; color: #2196f3;">18%</div>
                        <div style="font-size: 12px; color: #7f8c8d;">充电中</div>
                        <div style="font-size: 12px; color: #7f8c8d;">43.1°C</div>
                    </div>
                    <div style="background: #e3f2fd; border: 2px solid #2196f3; border-radius: 8px; padding: 10px; text-align: center;">
                        <div style="font-weight: bold; color: #2196f3; margin-bottom: 5px;">电池组 #3</div>
                        <div style="font-size: 18px; font-weight: bold; color: #2196f3;">22%</div>
                        <div style="font-size: 12px; color: #7f8c8d;">充电中</div>
                        <div style="font-size: 12px; color: #7f8c8d;">42.8°C</div>
                    </div>
                    <div style="background: #f5f5f5; border: 2px solid #95a5a6; border-radius: 8px; padding: 10px; text-align: center;">
                        <div style="font-weight: bold; color: #95a5a6; margin-bottom: 5px;">电池组 #4</div>
                        <div style="font-size: 18px; font-weight: bold; color: #95a5a6;">12%</div>
                        <div style="font-size: 12px; color: #7f8c8d;">待机</div>
                        <div style="font-size: 12px; color: #7f8c8d;">40.5°C</div>
                    </div>
                    <div style="background: #e3f2fd; border: 2px solid #2196f3; border-radius: 8px; padding: 10px; text-align: center;">
                        <div style="font-weight: bold; color: #2196f3; margin-bottom: 5px;">电池组 #5</div>
                        <div style="font-size: 18px; font-weight: bold; color: #2196f3;">15%</div>
                        <div style="font-size: 12px; color: #7f8c8d;">充电中</div>
                        <div style="font-size: 12px; color: #7f8c8d;">41.8°C</div>
                    </div>
                </div>
                <div style="height: 200px; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #6c757d; margin-top: 15px;">
                    📊 SOC变化曲线图
                    <br>显示各电池组SOC实时变化趋势
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-title">⚡ 充放电控制</div>
                <div style="margin-top: 15px;">
                    <div style="background: #e8f5e8; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #27ae60; margin-bottom: 10px;">🔋 当前模式: 快速充电</div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>充电功率:</span>
                            <span style="font-weight: bold;">85.2 kW</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>目标SOC:</span>
                            <span style="font-weight: bold;">80%</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>预计完成:</span>
                            <span style="font-weight: bold;">2小时15分</span>
                        </div>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <button style="width: 100%; background: #27ae60; color: white; border: none; padding: 10px; border-radius: 5px; margin-bottom: 8px; cursor: pointer;">🔋 开始充电</button>
                        <button style="width: 100%; background: #e74c3c; color: white; border: none; padding: 10px; border-radius: 5px; margin-bottom: 8px; cursor: pointer;">⚡ 开始放电</button>
                        <button style="width: 100%; background: #95a5a6; color: white; border: none; padding: 10px; border-radius: 5px; margin-bottom: 8px; cursor: pointer;">⏸️ 暂停操作</button>
                        <button style="width: 100%; background: #f39c12; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">🚨 紧急停止</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-title">📊 数据展示</div>
                <div class="feature-desc">实时数据图表和统计信息展示</div>
            </div>
            <div class="feature-card">
                <div class="feature-title">⚙️ 功能控制</div>
                <div class="feature-desc">相关功能的操作控制界面</div>
            </div>
            <div class="feature-card">
                <div class="feature-title">📈 趋势分析</div>
                <div class="feature-desc">历史数据趋势分析和预测</div>
            </div>
            <div class="feature-card">
                <div class="feature-title">🔔 告警提醒</div>
                <div class="feature-desc">相关告警信息和处理建议</div>
            </div>
        </div>
    </div>

    <div style="text-align: center; padding: 40px; color: rgba(0,0,0,0.6);">
        <p>© 2025 VPP-AI 储能管理系统 - 实时监控</p>
        <p>原型界面展示，实际功能正在开发中</p>
    </div>
</body>
</html>