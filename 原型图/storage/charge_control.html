<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>充放电控制 - VPP-AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #74b9ff22 0%, #74b9ff44 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .title {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb a {
            color: #3498db;
            text-decoration: none;
            margin: 0 5px;
        }
        
        .content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            min-height: 500px;
        }
        
        .back-btn {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 20px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #74b9ff;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .feature-desc {
            color: #7f8c8d;
            line-height: 1.6;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #27ae60;
            margin-right: 8px;
        }
        
        .placeholder {
            text-align: center;
            color: #7f8c8d;
            font-size: 18px;
            margin: 50px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="title">
                <span>🔋</span>
                <span>充放电控制</span>
            </h1>
            <p class="subtitle">充放电策略控制</p>
            <div class="nav-breadcrumb">
                <a href="../index.html">🏠 首页</a> > 
                <a href="../dashboard/main.html">📊 控制台</a> > 
                <a href="index.html">🔋 储能管理系统</a> > 
                <span>充放电控制</span>
            </div>
        </div>
    </div>

    <a href="index.html" class="back-btn">← 返回储能管理系统</a>

    <div class="content">
        <h2 style="color: #2c3e50; margin-bottom: 20px;">
            <span class="status-indicator"></span>充放电控制
        </h2>
        
        <!-- 充放电控制面板 -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
            <div class="feature-card">
                <div class="feature-title">🔋 充电控制</div>
                <div style="margin-top: 15px;">
                    <div style="background: #e8f5e8; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #27ae60; margin-bottom: 10px;">当前状态: 快速充电中</div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>充电功率:</span>
                            <span style="font-weight: bold;">85.2 kW</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>充电电流:</span>
                            <span style="font-weight: bold;">213.0 A</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>预计完成时间:</span>
                            <span style="font-weight: bold;">2小时15分</span>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; font-weight: bold; margin-bottom: 5px;">目标SOC:</label>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <input type="range" min="20" max="100" value="80" style="flex: 1;">
                            <span style="font-weight: bold; min-width: 40px;">80%</span>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; font-weight: bold; margin-bottom: 5px;">充电功率限制:</label>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <input type="range" min="10" max="100" value="85" style="flex: 1;">
                            <span style="font-weight: bold; min-width: 50px;">85 kW</span>
                        </div>
                    </div>
                    
                    <div style="display: flex; gap: 8px;">
                        <button style="flex: 1; background: #27ae60; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">🚀 开始充电</button>
                        <button style="flex: 1; background: #f39c12; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">⏸️ 暂停充电</button>
                    </div>
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-title">⚡ 放电控制</div>
                <div style="margin-top: 15px;">
                    <div style="background: #f8d7da; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #721c24; margin-bottom: 10px;">当前状态: 待机中</div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>放电功率:</span>
                            <span style="font-weight: bold;">0 kW</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>可放电时长:</span>
                            <span style="font-weight: bold;">1.2 小时</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>最低SOC限制:</span>
                            <span style="font-weight: bold;">10%</span>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; font-weight: bold; margin-bottom: 5px;">最低SOC:</label>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <input type="range" min="5" max="30" value="10" style="flex: 1;">
                            <span style="font-weight: bold; min-width: 40px;">10%</span>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; font-weight: bold; margin-bottom: 5px;">放电功率限制:</label>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <input type="range" min="10" max="100" value="75" style="flex: 1;">
                            <span style="font-weight: bold; min-width: 50px;">75 kW</span>
                        </div>
                    </div>
                    
                    <div style="display: flex; gap: 8px;">
                        <button style="flex: 1; background: #e74c3c; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">⚡ 开始放电</button>
                        <button style="flex: 1; background: #95a5a6; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">⏹️ 停止放电</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 充放电策略 -->
        <div class="feature-card">
            <div class="feature-title">🧠 智能充放电策略</div>
            <div style="margin-top: 15px;">
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
                    <div style="background: #e8f5e8; border-radius: 8px; padding: 15px; text-align: center; cursor: pointer; border: 2px solid #27ae60;">
                        <div style="font-size: 20px; margin-bottom: 10px;">💰</div>
                        <div style="font-weight: bold; color: #27ae60; margin-bottom: 5px;">峰谷套利</div>
                        <div style="font-size: 12px; color: #7f8c8d;">谷时充电，峰时放电</div>
                        <div style="font-size: 12px; color: #27ae60; margin-top: 5px;">✅ 当前策略</div>
                    </div>
                    <div style="background: #f8f9fa; border-radius: 8px; padding: 15px; text-align: center; cursor: pointer; border: 2px solid #ddd;">
                        <div style="font-size: 20px; margin-bottom: 10px;">🌞</div>
                        <div style="font-weight: bold; color: #f39c12; margin-bottom: 5px;">光伏优先</div>
                        <div style="font-size: 12px; color: #7f8c8d;">优先存储光伏电量</div>
                        <div style="font-size: 12px; color: #7f8c8d; margin-top: 5px;">点击启用</div>
                    </div>
                    <div style="background: #f8f9fa; border-radius: 8px; padding: 15px; text-align: center; cursor: pointer; border: 2px solid #ddd;">
                        <div style="font-size: 20px; margin-bottom: 10px;">⚡</div>
                        <div style="font-weight: bold; color: #6c5ce7; margin-bottom: 5px;">需求响应</div>
                        <div style="font-size: 12px; color: #7f8c8d;">参与电网需求响应</div>
                        <div style="font-size: 12px; color: #7f8c8d; margin-top: 5px;">点击启用</div>
                    </div>
                </div>
            </div>
        </div>
                <div class="feature-desc">实时数据图表和统计信息展示</div>
            </div>
            <div class="feature-card">
                <div class="feature-title">⚙️ 功能控制</div>
                <div class="feature-desc">相关功能的操作控制界面</div>
            </div>
            <div class="feature-card">
                <div class="feature-title">📈 趋势分析</div>
                <div class="feature-desc">历史数据趋势分析和预测</div>
            </div>
            <div class="feature-card">
                <div class="feature-title">🔔 告警提醒</div>
                <div class="feature-desc">相关告警信息和处理建议</div>
            </div>
        </div>
    </div>

    <div style="text-align: center; padding: 40px; color: rgba(0,0,0,0.6);">
        <p>© 2025 VPP-AI 储能管理系统 - 充放电控制</p>
        <p>原型界面展示，实际功能正在开发中</p>
    </div>
</body>
</html>