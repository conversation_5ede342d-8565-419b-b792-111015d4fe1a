<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VPP-AI 系统逻辑架构图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: auto;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .title {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 16px;
        }
        
        .architecture-container {
            margin-top: 100px;
            padding: 40px 20px;
            min-height: calc(100vh - 100px);
        }
        
        .layer {
            margin-bottom: 40px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .layer-title {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: bold;
        }
        
        .modules {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .module {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .module:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }
        
        .module::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .module:hover::before {
            left: 100%;
        }
        
        .module-icon {
            font-size: 24px;
            margin-bottom: 10px;
            display: block;
        }
        
        .module-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .module-desc {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .connections {
            position: relative;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .arrow {
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-top: 20px solid #3498db;
            margin: 0 10px;
        }
        
        .data-flow {
            background: #3498db;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            margin: 0 10px;
        }
        
        .legend {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            font-size: 12px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
            margin-right: 10px;
        }
        
        .presentation-layer { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
        .application-layer { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
        .service-layer { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
        .data-layer { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .infrastructure-layer { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="title">VPP-AI 系统逻辑架构图</h1>
            <p class="subtitle">点击任意模块查看对应的原型界面 | <a href="index.html" style="color: #3498db;">返回导航</a></p>
        </div>
    </div>

    <div class="architecture-container">
        <!-- 表现层 -->
        <div class="layer">
            <div class="layer-title">🖥️ 表现层 (Presentation Layer)</div>
            <div class="modules">
                <div class="module presentation-layer" onclick="window.open('dashboard/main.html', '_blank')">
                    <span class="module-icon">📊</span>
                    <div class="module-name">主控制台</div>
                    <div class="module-desc">实时监控总览</div>
                </div>
                <div class="module presentation-layer" onclick="window.open('dashboard/mobile.html', '_blank')">
                    <span class="module-icon">📱</span>
                    <div class="module-name">移动端界面</div>
                    <div class="module-desc">移动设备适配</div>
                </div>
                <div class="module presentation-layer" onclick="window.open('dashboard/3d_view.html', '_blank')">
                    <span class="module-icon">🏭</span>
                    <div class="module-name">3D可视化</div>
                    <div class="module-desc">三维场景展示</div>
                </div>
                <div class="module presentation-layer" onclick="window.open('reports/dashboard.html', '_blank')">
                    <span class="module-icon">📈</span>
                    <div class="module-name">报表中心</div>
                    <div class="module-desc">数据分析报告</div>
                </div>
            </div>
        </div>

        <div class="connections">
            <div class="arrow"></div>
            <div class="data-flow">用户交互</div>
            <div class="arrow"></div>
        </div>

        <!-- 应用层 -->
        <div class="layer">
            <div class="layer-title">🎯 应用层 (Application Layer)</div>
            <div class="modules">
                <div class="module application-layer" onclick="window.open('solar/monitoring.html', '_blank')">
                    <span class="module-icon">🌞</span>
                    <div class="module-name">光伏管理</div>
                    <div class="module-desc">发电监控预测</div>
                </div>
                <div class="module application-layer" onclick="window.open('storage/monitoring.html', '_blank')">
                    <span class="module-icon">🔋</span>
                    <div class="module-name">储能管理</div>
                    <div class="module-desc">电池状态控制</div>
                </div>
                <div class="module application-layer" onclick="window.open('load/monitoring.html', '_blank')">
                    <span class="module-icon">⚡</span>
                    <div class="module-name">负荷管理</div>
                    <div class="module-desc">用电需求调度</div>
                </div>
                <div class="module application-layer" onclick="window.open('grid/trading.html', '_blank')">
                    <span class="module-icon">💰</span>
                    <div class="module-name">电网交易</div>
                    <div class="module-desc">售电策略优化</div>
                </div>
                <div class="module application-layer" onclick="window.open('weather/monitoring.html', '_blank')">
                    <span class="module-icon">🌤️</span>
                    <div class="module-name">天气监控</div>
                    <div class="module-desc">气象数据分析</div>
                </div>
            </div>
        </div>

        <div class="connections">
            <div class="arrow"></div>
            <div class="data-flow">业务逻辑</div>
            <div class="arrow"></div>
        </div>

        <!-- 服务层 -->
        <div class="layer">
            <div class="layer-title">⚙️ 服务层 (Service Layer)</div>
            <div class="modules">
                <div class="module service-layer" onclick="window.open('optimization/ai_prediction.html', '_blank')">
                    <span class="module-icon">🤖</span>
                    <div class="module-name">AI预测服务</div>
                    <div class="module-desc">机器学习预测</div>
                </div>
                <div class="module service-layer" onclick="window.open('optimization/strategy.html', '_blank')">
                    <span class="module-icon">🧠</span>
                    <div class="module-name">优化引擎</div>
                    <div class="module-desc">智能决策算法</div>
                </div>
                <div class="module service-layer" onclick="window.open('digital_twin/simulation.html', '_blank')">
                    <span class="module-icon">🔬</span>
                    <div class="module-name">仿真服务</div>
                    <div class="module-desc">数字孪生建模</div>
                </div>
                <div class="module service-layer" onclick="window.open('system/notification.html', '_blank')">
                    <span class="module-icon">🔔</span>
                    <div class="module-name">通知服务</div>
                    <div class="module-desc">告警消息推送</div>
                </div>
                <div class="module service-layer" onclick="window.open('system/security.html', '_blank')">
                    <span class="module-icon">🔐</span>
                    <div class="module-name">安全服务</div>
                    <div class="module-desc">权限认证管理</div>
                </div>
            </div>
        </div>

        <div class="connections">
            <div class="arrow"></div>
            <div class="data-flow">数据处理</div>
            <div class="arrow"></div>
        </div>

        <!-- 数据层 -->
        <div class="layer">
            <div class="layer-title">💾 数据层 (Data Layer)</div>
            <div class="modules">
                <div class="module data-layer" onclick="window.open('system/database.html', '_blank')">
                    <span class="module-icon">🗄️</span>
                    <div class="module-name">关系数据库</div>
                    <div class="module-desc">PostgreSQL</div>
                </div>
                <div class="module data-layer" onclick="window.open('system/cache.html', '_blank')">
                    <span class="module-icon">⚡</span>
                    <div class="module-name">缓存系统</div>
                    <div class="module-desc">Redis</div>
                </div>
                <div class="module data-layer" onclick="window.open('system/timeseries.html', '_blank')">
                    <span class="module-icon">📊</span>
                    <div class="module-name">时序数据库</div>
                    <div class="module-desc">MongoDB</div>
                </div>
                <div class="module data-layer" onclick="window.open('system/file_storage.html', '_blank')">
                    <span class="module-icon">📁</span>
                    <div class="module-name">文件存储</div>
                    <div class="module-desc">模型文件管理</div>
                </div>
            </div>
        </div>

        <div class="connections">
            <div class="arrow"></div>
            <div class="data-flow">基础设施</div>
            <div class="arrow"></div>
        </div>

        <!-- 基础设施层 -->
        <div class="layer">
            <div class="layer-title">🏗️ 基础设施层 (Infrastructure Layer)</div>
            <div class="modules">
                <div class="module infrastructure-layer" onclick="window.open('system/monitoring.html', '_blank')">
                    <span class="module-icon">📡</span>
                    <div class="module-name">系统监控</div>
                    <div class="module-desc">性能指标监控</div>
                </div>
                <div class="module infrastructure-layer" onclick="window.open('system/logging.html', '_blank')">
                    <span class="module-icon">📝</span>
                    <div class="module-name">日志系统</div>
                    <div class="module-desc">操作日志记录</div>
                </div>
                <div class="module infrastructure-layer" onclick="window.open('system/backup.html', '_blank')">
                    <span class="module-icon">💾</span>
                    <div class="module-name">备份恢复</div>
                    <div class="module-desc">数据备份策略</div>
                </div>
                <div class="module infrastructure-layer" onclick="window.open('system/deployment.html', '_blank')">
                    <span class="module-icon">🚀</span>
                    <div class="module-name">部署运维</div>
                    <div class="module-desc">容器化部署</div>
                </div>
            </div>
        </div>
    </div>

    <div class="legend">
        <div style="font-weight: bold; margin-bottom: 10px;">图例说明</div>
        <div class="legend-item">
            <div class="legend-color presentation-layer"></div>
            <span>表现层 - 用户界面</span>
        </div>
        <div class="legend-item">
            <div class="legend-color application-layer"></div>
            <span>应用层 - 业务功能</span>
        </div>
        <div class="legend-item">
            <div class="legend-color service-layer"></div>
            <span>服务层 - 核心服务</span>
        </div>
        <div class="legend-item">
            <div class="legend-color data-layer"></div>
            <span>数据层 - 数据存储</span>
        </div>
        <div class="legend-item">
            <div class="legend-color infrastructure-layer"></div>
            <span>基础设施层 - 系统支撑</span>
        </div>
    </div>
</body>
</html>
