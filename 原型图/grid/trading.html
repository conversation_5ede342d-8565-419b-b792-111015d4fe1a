<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电力交易 - VPP-AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #6c5ce722 0%, #6c5ce744 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .title {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .nav-breadcrumb a {
            color: #3498db;
            text-decoration: none;
            margin: 0 5px;
        }
        
        .content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            min-height: 500px;
        }
        
        .back-btn {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 20px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #6c5ce7;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .feature-desc {
            color: #7f8c8d;
            line-height: 1.6;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #27ae60;
            margin-right: 8px;
        }
        
        .placeholder {
            text-align: center;
            color: #7f8c8d;
            font-size: 18px;
            margin: 50px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="title">
                <span>🔌</span>
                <span>电力交易</span>
            </h1>
            <p class="subtitle">电力交易管理</p>
            <div class="nav-breadcrumb">
                <a href="../index.html">🏠 首页</a> > 
                <a href="../dashboard/main.html">📊 控制台</a> > 
                <a href="index.html">🔌 电网交易系统</a> > 
                <span>电力交易</span>
            </div>
        </div>
    </div>

    <a href="index.html" class="back-btn">← 返回电网交易系统</a>

    <div class="content">
        <h2 style="color: #2c3e50; margin-bottom: 20px;">
            <span class="status-indicator"></span>电力交易
        </h2>
        
        <!-- 交易概览 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">
            <div class="feature-card" style="text-align: center; border-left: 4px solid #6c5ce7;">
                <div style="font-size: 28px; font-weight: bold; color: #6c5ce7; margin-bottom: 5px;">¥2,456</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">今日收益</div>
                <div style="font-size: 12px; color: #27ae60;">📈 +15.2% 较昨日</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #00b894;">
                <div style="font-size: 28px; font-weight: bold; color: #00b894; margin-bottom: 5px;">156.8 kWh</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">今日售电量</div>
                <div style="font-size: 12px; color: #27ae60;">⚡ 上网电量</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #fd79a8;">
                <div style="font-size: 28px; font-weight: bold; color: #fd79a8; margin-bottom: 5px;">0.85 元/kWh</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">当前电价</div>
                <div style="font-size: 12px; color: #f39c12;">⏰ 平时电价</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #fdcb6e;">
                <div style="font-size: 28px; font-weight: bold; color: #fdcb6e; margin-bottom: 5px;">峰时</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">下个时段</div>
                <div style="font-size: 12px; color: #e74c3c;">🕕 18:00 开始</div>
            </div>
        </div>

        <!-- 交易详情 -->
        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin-bottom: 30px;">
            <div class="feature-card">
                <div class="feature-title">💹 实时电价与交易曲线</div>
                <div style="height: 300px; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #6c757d; margin-top: 15px;">
                    📊 电价变化与交易量曲线图
                    <br>显示24小时电价变化和交易策略执行情况
                </div>
                <div style="display: flex; justify-content: space-between; margin-top: 10px; font-size: 12px; color: #7f8c8d;">
                    <span>🔵 实时电价</span>
                    <span>🟢 售电量</span>
                    <span>🔴 购电量</span>
                    <span>🟡 净收益</span>
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-title">⚡ 交易策略控制</div>
                <div style="margin-top: 15px;">
                    <div style="background: #e8f5e8; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #27ae60; margin-bottom: 10px;">🤖 当前策略: 智能套利</div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>售电阈值:</span>
                            <span style="font-weight: bold;">1.0 元/kWh</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>购电阈值:</span>
                            <span style="font-weight: bold;">0.5 元/kWh</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>预期收益:</span>
                            <span style="font-weight: bold; color: #27ae60;">+18.5%</span>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <div style="font-weight: bold; margin-bottom: 10px;">📋 交易计划</div>
                        <div style="font-size: 12px; color: #7f8c8d; margin-bottom: 5px;">18:00-22:00 峰时售电</div>
                        <div style="font-size: 12px; color: #7f8c8d; margin-bottom: 5px;">23:00-07:00 谷时购电</div>
                        <div style="font-size: 12px; color: #7f8c8d;">预计净收益: ¥3,200</div>
                    </div>
                    
                    <div>
                        <button style="width: 100%; background: #27ae60; color: white; border: none; padding: 10px; border-radius: 5px; margin-bottom: 8px; cursor: pointer;">🚀 启动自动交易</button>
                        <button style="width: 100%; background: #f39c12; color: white; border: none; padding: 10px; border-radius: 5px; margin-bottom: 8px; cursor: pointer;">⚙️ 策略设置</button>
                        <button style="width: 100%; background: #e74c3c; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">⏸️ 暂停交易</button>
                    </div>
                </div>
            </div>
        </div>
                <div class="feature-desc">实时数据图表和统计信息展示</div>
            </div>
            <div class="feature-card">
                <div class="feature-title">⚙️ 功能控制</div>
                <div class="feature-desc">相关功能的操作控制界面</div>
            </div>
            <div class="feature-card">
                <div class="feature-title">📈 趋势分析</div>
                <div class="feature-desc">历史数据趋势分析和预测</div>
            </div>
            <div class="feature-card">
                <div class="feature-title">🔔 告警提醒</div>
                <div class="feature-desc">相关告警信息和处理建议</div>
            </div>
        </div>
    </div>

    <div style="text-align: center; padding: 40px; color: rgba(0,0,0,0.6);">
        <p>© 2025 VPP-AI 电网交易系统 - 电力交易</p>
        <p>原型界面展示，实际功能正在开发中</p>
    </div>
</body>
</html>