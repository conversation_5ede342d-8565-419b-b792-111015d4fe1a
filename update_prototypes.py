#!/usr/bin/env python3
"""
VPP-AI 原型界面内容更新脚本
=============================

为原型界面添加具体的、有意义的内容
"""

import os
import re
from pathlib import Path

# 定义具体的页面内容
PAGE_CONTENTS = {
    'load/monitoring.html': {
        'title': '负荷实时监控',
        'content': '''
        <!-- 负荷概览 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">
            <div class="feature-card" style="text-align: center; border-left: 4px solid #00b894;">
                <div style="font-size: 28px; font-weight: bold; color: #00b894; margin-bottom: 5px;">89.2 kW</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">当前总负荷</div>
                <div style="font-size: 12px; color: #00b894;">📈 负荷率 67%</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #6c5ce7;">
                <div style="font-size: 28px; font-weight: bold; color: #6c5ce7; margin-bottom: 5px;">125.6 kW</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">峰值负荷</div>
                <div style="font-size: 12px; color: #7f8c8d;">今日 14:30</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #fd79a8;">
                <div style="font-size: 28px; font-weight: bold; color: #fd79a8; margin-bottom: 5px;">15.2 kW</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">可调节负荷</div>
                <div style="font-size: 12px; color: #00b894;">💡 灵活性 12%</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #fdcb6e;">
                <div style="font-size: 28px; font-weight: bold; color: #fdcb6e; margin-bottom: 5px;">¥1,245</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">今日电费</div>
                <div style="font-size: 12px; color: #e74c3c;">📊 较昨日 +5.2%</div>
            </div>
        </div>

        <!-- 负荷设备状态 -->
        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin-bottom: 30px;">
            <div class="feature-card">
                <div class="feature-title">⚡ 负荷设备实时状态</div>
                <div style="margin-top: 15px;">
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
                        <div style="background: #e8f5e8; border-radius: 8px; padding: 15px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <span style="font-weight: bold; color: #27ae60;">🌡️ 中央空调</span>
                                <span style="background: #27ae60; color: white; padding: 2px 8px; border-radius: 10px; font-size: 12px;">运行中</span>
                            </div>
                            <div style="font-size: 20px; font-weight: bold; color: #27ae60; margin-bottom: 5px;">45.8 kW</div>
                            <div style="font-size: 12px; color: #7f8c8d;">设定温度: 24°C | 优先级: 高</div>
                        </div>
                        <div style="background: #e8f5e8; border-radius: 8px; padding: 15px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <span style="font-weight: bold; color: #27ae60;">💡 照明系统</span>
                                <span style="background: #27ae60; color: white; padding: 2px 8px; border-radius: 10px; font-size: 12px;">运行中</span>
                            </div>
                            <div style="font-size: 20px; font-weight: bold; color: #27ae60; margin-bottom: 5px;">12.3 kW</div>
                            <div style="font-size: 12px; color: #7f8c8d;">亮度: 80% | 优先级: 中</div>
                        </div>
                        <div style="background: #fff3cd; border-radius: 8px; padding: 15px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <span style="font-weight: bold; color: #856404;">🏭 生产设备</span>
                                <span style="background: #ffc107; color: white; padding: 2px 8px; border-radius: 10px; font-size: 12px;">调节中</span>
                            </div>
                            <div style="font-size: 20px; font-weight: bold; color: #856404; margin-bottom: 5px;">28.5 kW</div>
                            <div style="font-size: 12px; color: #7f8c8d;">负荷率: 75% | 优先级: 最高</div>
                        </div>
                        <div style="background: #f8d7da; border-radius: 8px; padding: 15px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <span style="font-weight: bold; color: #721c24;">🚗 充电桩</span>
                                <span style="background: #dc3545; color: white; padding: 2px 8px; border-radius: 10px; font-size: 12px;">已暂停</span>
                            </div>
                            <div style="font-size: 20px; font-weight: bold; color: #721c24; margin-bottom: 5px;">0 kW</div>
                            <div style="font-size: 12px; color: #7f8c8d;">需求响应暂停 | 优先级: 低</div>
                        </div>
                    </div>
                </div>
                <div style="height: 200px; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #6c757d; margin-top: 15px;">
                    📊 负荷功率实时曲线
                    <br>显示各类负荷的功率变化趋势
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-title">🎛️ 负荷控制面板</div>
                <div style="margin-top: 15px;">
                    <div style="margin-bottom: 20px;">
                        <div style="font-weight: bold; margin-bottom: 10px;">🌡️ 空调控制</div>
                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                            <span style="font-size: 14px;">温度:</span>
                            <input type="range" min="20" max="28" value="24" style="flex: 1;">
                            <span style="font-weight: bold;">24°C</span>
                        </div>
                        <button style="width: 100%; background: #27ae60; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">应用设置</button>
                    </div>
                    
                    <div style="margin-bottom: 20px;">
                        <div style="font-weight: bold; margin-bottom: 10px;">💡 照明控制</div>
                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                            <span style="font-size: 14px;">亮度:</span>
                            <input type="range" min="0" max="100" value="80" style="flex: 1;">
                            <span style="font-weight: bold;">80%</span>
                        </div>
                        <button style="width: 100%; background: #f39c12; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">调节亮度</button>
                    </div>
                    
                    <div style="margin-bottom: 20px;">
                        <div style="font-weight: bold; margin-bottom: 10px;">🚗 充电桩控制</div>
                        <button style="width: 100%; background: #27ae60; color: white; border: none; padding: 8px; border-radius: 5px; margin-bottom: 5px; cursor: pointer;">恢复充电</button>
                        <button style="width: 100%; background: #e74c3c; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">延迟充电</button>
                    </div>
                </div>
            </div>
        </div>
        '''
    },
    
    'grid/trading.html': {
        'title': '电力交易管理',
        'content': '''
        <!-- 交易概览 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">
            <div class="feature-card" style="text-align: center; border-left: 4px solid #6c5ce7;">
                <div style="font-size: 28px; font-weight: bold; color: #6c5ce7; margin-bottom: 5px;">¥2,456</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">今日收益</div>
                <div style="font-size: 12px; color: #27ae60;">📈 +15.2% 较昨日</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #00b894;">
                <div style="font-size: 28px; font-weight: bold; color: #00b894; margin-bottom: 5px;">156.8 kWh</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">今日售电量</div>
                <div style="font-size: 12px; color: #27ae60;">⚡ 上网电量</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #fd79a8;">
                <div style="font-size: 28px; font-weight: bold; color: #fd79a8; margin-bottom: 5px;">0.85 元/kWh</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">当前电价</div>
                <div style="font-size: 12px; color: #f39c12;">⏰ 平时电价</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #fdcb6e;">
                <div style="font-size: 28px; font-weight: bold; color: #fdcb6e; margin-bottom: 5px;">峰时</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">下个时段</div>
                <div style="font-size: 12px; color: #e74c3c;">🕕 18:00 开始</div>
            </div>
        </div>

        <!-- 交易详情 -->
        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin-bottom: 30px;">
            <div class="feature-card">
                <div class="feature-title">💹 实时电价与交易曲线</div>
                <div style="height: 300px; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #6c757d; margin-top: 15px;">
                    📊 电价变化与交易量曲线图
                    <br>显示24小时电价变化和交易策略执行情况
                </div>
                <div style="display: flex; justify-content: space-between; margin-top: 10px; font-size: 12px; color: #7f8c8d;">
                    <span>🔵 实时电价</span>
                    <span>🟢 售电量</span>
                    <span>🔴 购电量</span>
                    <span>🟡 净收益</span>
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-title">⚡ 交易策略控制</div>
                <div style="margin-top: 15px;">
                    <div style="background: #e8f5e8; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #27ae60; margin-bottom: 10px;">🤖 当前策略: 智能套利</div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>售电阈值:</span>
                            <span style="font-weight: bold;">1.0 元/kWh</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>购电阈值:</span>
                            <span style="font-weight: bold;">0.5 元/kWh</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>预期收益:</span>
                            <span style="font-weight: bold; color: #27ae60;">+18.5%</span>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <div style="font-weight: bold; margin-bottom: 10px;">📋 交易计划</div>
                        <div style="font-size: 12px; color: #7f8c8d; margin-bottom: 5px;">18:00-22:00 峰时售电</div>
                        <div style="font-size: 12px; color: #7f8c8d; margin-bottom: 5px;">23:00-07:00 谷时购电</div>
                        <div style="font-size: 12px; color: #7f8c8d;">预计净收益: ¥3,200</div>
                    </div>
                    
                    <div>
                        <button style="width: 100%; background: #27ae60; color: white; border: none; padding: 10px; border-radius: 5px; margin-bottom: 8px; cursor: pointer;">🚀 启动自动交易</button>
                        <button style="width: 100%; background: #f39c12; color: white; border: none; padding: 10px; border-radius: 5px; margin-bottom: 8px; cursor: pointer;">⚙️ 策略设置</button>
                        <button style="width: 100%; background: #e74c3c; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">⏸️ 暂停交易</button>
                    </div>
                </div>
            </div>
        </div>
        '''
    },
    
    'weather/monitoring.html': {
        'title': '天气实时监控',
        'content': '''
        <!-- 天气概览 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">
            <div class="feature-card" style="text-align: center; border-left: 4px solid #00cec9;">
                <div style="font-size: 28px; font-weight: bold; color: #00cec9; margin-bottom: 5px;">28.5°C</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">环境温度</div>
                <div style="font-size: 12px; color: #00b894;">🌡️ 适宜温度</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #fdcb6e;">
                <div style="font-size: 28px; font-weight: bold; color: #fdcb6e; margin-bottom: 5px;">850 W/m²</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">太阳辐照度</div>
                <div style="font-size: 12px; color: #00b894;">☀️ 辐照强烈</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #74b9ff;">
                <div style="font-size: 28px; font-weight: bold; color: #74b9ff; margin-bottom: 5px;">3.2 m/s</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">风速</div>
                <div style="font-size: 12px; color: #00b894;">🌬️ 微风</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #a29bfe;">
                <div style="font-size: 28px; font-weight: bold; color: #a29bfe; margin-bottom: 5px;">65%</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">相对湿度</div>
                <div style="font-size: 12px; color: #00b894;">💧 湿度适中</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #fd79a8;">
                <div style="font-size: 28px; font-weight: bold; color: #fd79a8; margin-bottom: 5px;">30%</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">云量覆盖</div>
                <div style="font-size: 12px; color: #00b894;">☁️ 少云</div>
            </div>
            <div class="feature-card" style="text-align: center; border-left: 4px solid #6c5ce7;">
                <div style="font-size: 28px; font-weight: bold; color: #6c5ce7; margin-bottom: 5px;">1013 hPa</div>
                <div style="color: #7f8c8d; margin-bottom: 5px;">大气压强</div>
                <div style="font-size: 12px; color: #00b894;">📊 标准气压</div>
            </div>
        </div>

        <!-- 天气详情 -->
        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin-bottom: 30px;">
            <div class="feature-card">
                <div class="feature-title">🌤️ 24小时天气变化趋势</div>
                <div style="height: 300px; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #6c757d; margin-top: 15px;">
                    📊 天气参数变化曲线图
                    <br>显示温度、湿度、辐照度、风速等参数的24小时变化趋势
                </div>
                <div style="display: flex; justify-content: space-between; margin-top: 10px; font-size: 12px; color: #7f8c8d;">
                    <span>🔴 温度</span>
                    <span>🔵 湿度</span>
                    <span>🟡 辐照度</span>
                    <span>🟢 风速</span>
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-title">🔮 天气预报</div>
                <div style="margin-top: 15px;">
                    <div style="background: #e8f5e8; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #27ae60; margin-bottom: 10px;">☀️ 今日: 晴转多云</div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>最高温度:</span>
                            <span style="font-weight: bold;">32°C</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>最低温度:</span>
                            <span style="font-weight: bold;">22°C</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>降水概率:</span>
                            <span style="font-weight: bold;">10%</span>
                        </div>
                    </div>
                    
                    <div style="background: #fff3cd; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #856404; margin-bottom: 10px;">🌦️ 明日: 多云转阴</div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>最高温度:</span>
                            <span style="font-weight: bold;">28°C</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>最低温度:</span>
                            <span style="font-weight: bold;">20°C</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>降水概率:</span>
                            <span style="font-weight: bold;">40%</span>
                        </div>
                    </div>
                    
                    <div style="background: #f8d7da; border-radius: 8px; padding: 15px;">
                        <div style="font-weight: bold; color: #721c24; margin-bottom: 10px;">🌧️ 后天: 小雨</div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>最高温度:</span>
                            <span style="font-weight: bold;">25°C</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>最低温度:</span>
                            <span style="font-weight: bold;">18°C</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>降水概率:</span>
                            <span style="font-weight: bold;">80%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        '''
    }
}

def update_page_content(file_path, content_data):
    """更新页面内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 查找并替换placeholder部分
        placeholder_pattern = r'<div class="placeholder">.*?</div>\s*<div class="feature-grid">.*?</div>'
        
        if re.search(placeholder_pattern, html_content, re.DOTALL):
            new_content = content_data['content'].strip()
            html_content = re.sub(placeholder_pattern, new_content, html_content, flags=re.DOTALL)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"✅ 更新页面: {file_path}")
            return True
        else:
            print(f"⚠️  未找到placeholder: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ 更新失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    base_dir = Path("原型图")
    updated_count = 0
    
    print("🚀 开始更新原型界面内容...")
    
    for page_path, content_data in PAGE_CONTENTS.items():
        full_path = base_dir / page_path
        if full_path.exists():
            if update_page_content(full_path, content_data):
                updated_count += 1
        else:
            print(f"❌ 文件不存在: {full_path}")
    
    print(f"\n✅ 完成更新，共更新了 {updated_count} 个页面")

if __name__ == "__main__":
    main()
