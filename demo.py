#!/usr/bin/env python3
"""
VPP-AI 系统演示脚本
==================

展示系统的核心功能和数据模型。
"""

import sys
import math
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.config import config
from src.core.logger import get_logger
from src.models import *
from src.models.storage import BatteryType
from src.models.load import LoadType, LoadPriority
from src.models.optimization import OptimizationType, OptimizationStatus

logger = get_logger(__name__)


def demo_config_system():
    """演示配置系统"""
    print("=" * 50)
    print("📋 配置系统演示")
    print("=" * 50)
    
    print(f"系统名称: {config.system.name}")
    print(f"系统版本: {config.system.version}")
    print(f"调试模式: {config.system.debug}")
    print()
    
    print("光伏系统配置:")
    print(f"  - 光伏板面积: {config.solar.panel_area} 平方米")
    print(f"  - 光伏板效率: {config.solar.panel_efficiency * 100}%")
    print(f"  - 额定容量: {config.solar.inverter_capacity} kW")
    print()
    
    print("储能系统配置:")
    print(f"  - 电池容量: {config.storage.capacity} kWh")
    print(f"  - 最大充电功率: {config.storage.max_charge_power} kW")
    print(f"  - 充电效率: {config.storage.charge_efficiency * 100}%")
    print()


def demo_data_models():
    """演示数据模型"""
    print("=" * 50)
    print("🏗️  数据模型演示")
    print("=" * 50)
    
    # 创建光伏板实例
    solar_panel = SolarPanel(
        name="演示光伏板",
        description="屋顶光伏发电系统",
        location="工厂屋顶",
        area=1000.0,
        efficiency=0.22,
        rated_power=220.0,
        orientation=180,
        tilt=30
    )
    print("✅ 光伏板模型创建成功")
    print(f"   名称: {solar_panel.name}")
    print(f"   面积: {solar_panel.area} 平方米")
    print(f"   额定功率: {solar_panel.rated_power} kW")
    print()
    
    # 创建电池实例
    battery = Battery(
        name="演示储能电池",
        description="锂电池储能系统",
        location="储能室",
        battery_type=BatteryType.LITHIUM_ION,
        nominal_capacity=500.0,
        usable_capacity=450.0,
        max_charge_power=100.0,
        max_discharge_power=100.0
    )
    print("✅ 电池模型创建成功")
    print(f"   名称: {battery.name}")
    print(f"   类型: {battery.battery_type.value}")
    print(f"   容量: {battery.nominal_capacity} kWh")
    print()
    
    # 创建负荷实例
    load = Load(
        name="中央空调",
        description="办公楼中央空调系统",
        location="办公楼",
        load_type=LoadType.HVAC,
        priority=LoadPriority.HIGH,
        rated_power=150.0,
        flexibility=0.3
    )
    print("✅ 负荷模型创建成功")
    print(f"   名称: {load.name}")
    print(f"   类型: {load.load_type.value}")
    print(f"   优先级: {load.priority.value}")
    print(f"   额定功率: {load.rated_power} kW")
    print()


def demo_time_series_data():
    """演示时序数据"""
    print("=" * 50)
    print("📊 时序数据演示")
    print("=" * 50)
    
    # 模拟一天的光伏发电数据
    base_time = datetime.now().replace(hour=6, minute=0, second=0, microsecond=0)
    
    print("模拟24小时光伏发电数据:")
    print("时间\t\t辐照度(W/m²)\t功率(kW)\t温度(°C)")
    print("-" * 60)
    
    for hour in range(24):
        current_time = base_time + timedelta(hours=hour)
        
        # 模拟太阳辐照度 (简单的正弦波模型)
        if 6 <= hour <= 18:  # 白天
            irradiance = max(0, 800 * abs(math.sin((hour - 6) * math.pi / 12)))
        else:  # 夜晚
            irradiance = 0
        
        # 模拟功率输出 (考虑效率)
        power = irradiance * 0.22 * 1000 / 1000  # 转换为kW
        
        # 模拟温度
        temperature = 20 + 10 * math.sin((hour - 6) * math.pi / 12)
        
        solar_data = SolarData(
            timestamp=current_time,
            device_id=1,
            panel_id=1,
            power_output=power,
            irradiance=irradiance,
            panel_temperature=temperature
        )
        
        print(f"{current_time.strftime('%H:%M')}\t\t{irradiance:.0f}\t\t{power:.1f}\t\t{temperature:.1f}")
    
    print()


def demo_optimization_scenario():
    """演示优化场景"""
    print("=" * 50)
    print("🧠 优化场景演示")
    print("=" * 50)
    
    # 创建优化结果实例
    optimization = OptimizationResult(
        optimization_id="DEMO_OPT_001",
        optimization_type=OptimizationType.REVENUE_MAXIMIZATION,
        status=OptimizationStatus.COMPLETED,
        start_time=datetime.now(),
        end_time=datetime.now() + timedelta(hours=1),
        optimization_horizon=24,
        objective_function="maximize_profit",
        objective_value=1250.50,
        total_cost=800.00,
        total_revenue=2050.50,
        net_profit=1250.50,
        peak_demand=180.5,
        peak_reduction=25.3,
        renewable_energy_ratio=0.75
    )
    
    print("✅ 优化结果创建成功")
    print(f"   优化ID: {optimization.optimization_id}")
    print(f"   优化类型: {optimization.optimization_type.value}")
    print(f"   状态: {optimization.status.value}")
    print(f"   目标函数值: {optimization.objective_value:.2f}")
    print(f"   净利润: {optimization.net_profit:.2f} 元")
    print(f"   可再生能源比例: {optimization.renewable_energy_ratio * 100:.1f}%")
    print()


def demo_system_integration():
    """演示系统集成"""
    print("=" * 50)
    print("🔗 系统集成演示")
    print("=" * 50)
    
    print("系统组件集成状态:")
    components = [
        ("配置管理", "✅ 正常"),
        ("日志系统", "✅ 正常"),
        ("数据模型", "✅ 正常"),
        ("数据访问层", "✅ 正常"),
        ("命令行工具", "✅ 正常"),
        ("数据库连接", "⚠️  需要配置"),
        ("AI预测模块", "🔄 开发中"),
        ("优化引擎", "📋 待开发"),
        ("Web界面", "📋 待开发")
    ]
    
    for component, status in components:
        print(f"   {component}: {status}")
    
    print()
    print("🎯 系统已具备:")
    print("   - 完整的数据模型体系")
    print("   - 灵活的配置管理")
    print("   - 强大的日志系统")
    print("   - 标准化的数据访问接口")
    print("   - 命令行管理工具")
    print()
    print("📝 下一步开发:")
    print("   - AI预测模型训练")
    print("   - 优化算法实现")
    print("   - Web可视化界面")
    print("   - 实时数据采集")


def main():
    """主演示函数"""
    print("🚀 VPP-AI 新能源微网决策系统演示")
    print("=" * 60)
    print()
    
    try:
        demo_config_system()
        demo_data_models()
        demo_time_series_data()
        
        demo_optimization_scenario()
        demo_system_integration()
        
        print("=" * 60)
        print("✅ 演示完成！系统运行正常。")
        print("💡 您可以运行以下命令进一步探索:")
        print("   python main.py run      # 运行系统")
        print("   python main.py --help   # 查看帮助")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        logger.error(f"演示失败: {e}")


if __name__ == "__main__":
    main()
