# VPP-AI 新能源微网决策系统依赖包
# =======================================

# 核心框架
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0
sqlalchemy==2.0.23
alembic==1.13.0

# 数据处理
pandas==2.1.4
numpy==1.25.2
scipy==1.11.4

# 机器学习和AI
scikit-learn==1.3.2
tensorflow==2.15.0
torch==2.1.2
transformers==4.36.0
xgboost==2.0.2
lightgbm==4.1.0

# 时间序列预测
prophet==1.1.5
statsmodels==0.14.1

# 数据库
psycopg2-binary==2.9.9
redis==5.0.1
pymongo==4.6.0

# 可视化
plotly==5.17.0
matplotlib==3.8.2
seaborn==0.13.0
dash==2.16.1

# 天气数据API
requests==2.31.0
aiohttp==3.9.1

# 配置管理
python-dotenv==1.0.0
pyyaml==6.0.1

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1

# 工具
click==8.1.7
rich==13.7.0
loguru==0.7.2

# 数值优化
cvxpy==1.4.1
pulp==2.7.0

# 异步任务
celery==5.3.4
